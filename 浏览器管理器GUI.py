#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
浏览器多账号绿色版 - 图形界面管理器
版本: v2.2.1
作者: 浏览器多账号绿色版团队
描述: 基于Tkinter的图形界面，提供直观的浏览器管理功能
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import threading
from pathlib import Path
from 配置管理器 import 配置管理器
from 浏览器管理器 import 浏览器管理器

class 浏览器管理器GUI:
    """图形界面浏览器管理器"""

    def __init__(self):
        """初始化GUI"""
        self.配置器 = 配置管理器()
        self.浏览器管理器实例 = 浏览器管理器()

        # 创建主窗口
        self.root = tk.Tk()
        self.root.title("浏览器多账号绿色版 v2.2.1")
        self.root.geometry("1000x700")
        self.root.resizable(True, True)

        # 设置窗口图标（如果存在）
        try:
            icon_path = Path("默认图标/chrome.ico")
            if icon_path.exists():
                self.root.iconbitmap(str(icon_path))
        except:
            pass

        # 应用主题
        self._应用主题()

        # 创建界面
        self._创建菜单栏()
        self._创建工具栏()
        self._创建主界面()
        self._创建状态栏()

        # 初始化数据
        self._刷新浏览器列表()

        print("🎨 图形界面初始化完成")

    def _应用主题(self):
        """应用主题样式"""
        try:
            # 获取主题配置
            主题配置 = self.配置器.获取项目配置('界面配置.颜色主题.现代蓝色主题')

            if 主题配置:
                # 设置窗口背景色
                self.root.configure(bg=主题配置.get('背景色', '#FFFFFF'))

                # 配置ttk样式
                style = ttk.Style()
                style.theme_use('clam')

                # 自定义样式
                style.configure('Title.TLabel',
                              font=('Microsoft YaHei UI', 12, 'bold'),
                              foreground=主题配置.get('主色调', '#2196F3'))

                style.configure('Custom.Treeview',
                              font=('Microsoft YaHei UI', 9))

                style.configure('Custom.TButton',
                              font=('Microsoft YaHei UI', 9))
        except Exception as e:
            print(f"⚠️ 主题应用失败: {e}")

    def _创建菜单栏(self):
        """创建菜单栏"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)

        # 文件菜单
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="文件", menu=file_menu)
        file_menu.add_command(label="新建浏览器实例", command=self._新建浏览器实例)
        file_menu.add_separator()
        file_menu.add_command(label="导入配置", command=self._导入配置)
        file_menu.add_command(label="导出配置", command=self._导出配置)
        file_menu.add_separator()
        file_menu.add_command(label="退出", command=self.root.quit)

        # 工具菜单
        tools_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="工具", menu=tools_menu)
        tools_menu.add_command(label="批量创建", command=self._批量创建)
        tools_menu.add_command(label="清理临时文件", command=self._清理临时文件)
        tools_menu.add_command(label="检查更新", command=self._检查更新)

        # 帮助菜单
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="帮助", menu=help_menu)
        help_menu.add_command(label="使用说明", command=self._显示使用说明)
        help_menu.add_command(label="关于", command=self._显示关于)

    def _创建工具栏(self):
        """创建工具栏"""
        toolbar = ttk.Frame(self.root)
        toolbar.pack(fill=tk.X, padx=5, pady=2)

        # 工具栏按钮
        ttk.Button(toolbar, text="🆕 新建", style='Custom.TButton',
                  command=self._新建浏览器实例).pack(side=tk.LEFT, padx=2)

        ttk.Button(toolbar, text="🚀 启动", style='Custom.TButton',
                  command=self._启动选中浏览器).pack(side=tk.LEFT, padx=2)

        ttk.Button(toolbar, text="🖥️ 发送到桌面", style='Custom.TButton',
                  command=self._发送到桌面).pack(side=tk.LEFT, padx=2)

        ttk.Button(toolbar, text="🗑️ 删除", style='Custom.TButton',
                  command=self._删除选中浏览器).pack(side=tk.LEFT, padx=2)

        ttk.Separator(toolbar, orient=tk.VERTICAL).pack(side=tk.LEFT, fill=tk.Y, padx=5)

        ttk.Button(toolbar, text="🔄 刷新", style='Custom.TButton',
                  command=self._刷新浏览器列表).pack(side=tk.LEFT, padx=2)

        ttk.Button(toolbar, text="⚙️ 设置", style='Custom.TButton',
                  command=self._打开设置).pack(side=tk.LEFT, padx=2)

    def _创建主界面(self):
        """创建主界面"""
        # 创建主框架
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 左侧：浏览器列表
        left_frame = ttk.LabelFrame(main_frame, text="浏览器实例列表", padding=10)
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 5))

        # 创建Treeview
        columns = ('名称', '状态', '图标', '快捷方式')
        self.tree = ttk.Treeview(left_frame, columns=columns, show='headings', style='Custom.Treeview')

        # 设置列标题
        self.tree.heading('名称', text='浏览器名称')
        self.tree.heading('状态', text='状态')
        self.tree.heading('图标', text='图标')
        self.tree.heading('快捷方式', text='快捷方式')

        # 设置列宽
        self.tree.column('名称', width=200)
        self.tree.column('状态', width=80)
        self.tree.column('图标', width=80)
        self.tree.column('快捷方式', width=80)

        # 添加滚动条
        scrollbar = ttk.Scrollbar(left_frame, orient=tk.VERTICAL, command=self.tree.yview)
        self.tree.configure(yscrollcommand=scrollbar.set)

        # 布局
        self.tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 绑定双击事件
        self.tree.bind('<Double-1>', self._双击启动浏览器)

        # 右侧：详细信息和操作
        right_frame = ttk.LabelFrame(main_frame, text="详细信息", padding=10)
        right_frame.pack(side=tk.RIGHT, fill=tk.Y, padx=(5, 0))
        right_frame.configure(width=300)

        # 详细信息显示
        self.info_text = tk.Text(right_frame, width=35, height=20, wrap=tk.WORD)
        info_scrollbar = ttk.Scrollbar(right_frame, orient=tk.VERTICAL, command=self.info_text.yview)
        self.info_text.configure(yscrollcommand=info_scrollbar.set)

        self.info_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        info_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 操作按钮框架
        button_frame = ttk.Frame(right_frame)
        button_frame.pack(fill=tk.X, pady=(10, 0))

        ttk.Button(button_frame, text="启动浏览器",
                  command=self._启动选中浏览器).pack(fill=tk.X, pady=2)
        ttk.Button(button_frame, text="发送到桌面",
                  command=self._发送到桌面).pack(fill=tk.X, pady=2)
        ttk.Button(button_frame, text="打开目录",
                  command=self._打开浏览器目录).pack(fill=tk.X, pady=2)
        ttk.Button(button_frame, text="删除实例",
                  command=self._删除选中浏览器).pack(fill=tk.X, pady=2)

        # 绑定选择事件
        self.tree.bind('<<TreeviewSelect>>', self._选择浏览器)

    def _创建状态栏(self):
        """创建状态栏"""
        self.status_bar = ttk.Frame(self.root)
        self.status_bar.pack(fill=tk.X, side=tk.BOTTOM)

        self.status_label = ttk.Label(self.status_bar, text="就绪")
        self.status_label.pack(side=tk.LEFT, padx=5, pady=2)

        # 右侧显示浏览器数量
        self.count_label = ttk.Label(self.status_bar, text="")
        self.count_label.pack(side=tk.RIGHT, padx=5, pady=2)

    def _刷新浏览器列表(self):
        """刷新浏览器列表"""
        try:
            # 清空现有项目
            for item in self.tree.get_children():
                self.tree.delete(item)

            # 获取浏览器列表
            浏览器列表 = self.浏览器管理器实例.获取浏览器列表()

            # 添加到树形控件
            for 浏览器 in 浏览器列表:
                图标状态 = "✅" if 浏览器.get('图标文件') else "❌"
                快捷方式状态 = "✅" if 浏览器.get('快捷方式') else "❌"

                self.tree.insert('', tk.END, values=(
                    浏览器['名称'],
                    浏览器['状态'],
                    图标状态,
                    快捷方式状态
                ))

            # 更新状态栏
            self.count_label.config(text=f"共 {len(浏览器列表)} 个浏览器实例")
            self.status_label.config(text="浏览器列表已刷新")

        except Exception as e:
            messagebox.showerror("错误", f"刷新浏览器列表失败: {e}")

    def _选择浏览器(self, event):
        """选择浏览器时显示详细信息"""
        try:
            selection = self.tree.selection()
            if not selection:
                return

            item = self.tree.item(selection[0])
            浏览器名称 = item['values'][0]

            # 获取详细信息
            浏览器列表 = self.浏览器管理器实例.获取浏览器列表()
            浏览器信息 = None
            for 浏览器 in 浏览器列表:
                if 浏览器['名称'] == 浏览器名称:
                    浏览器信息 = 浏览器
                    break

            if 浏览器信息:
                # 显示详细信息
                self.info_text.delete(1.0, tk.END)
                info = f"""浏览器详细信息

名称: {浏览器信息['名称']}
状态: {浏览器信息['状态']}
路径: {浏览器信息['路径']}

Chrome程序: {'✅ 存在' if 浏览器信息['Chrome程序'] else '❌ 缺失'}
数据目录: {浏览器信息['数据目录'] or '❌ 缺失'}
图标文件: {浏览器信息['图标文件'] or '❌ 缺失'}
快捷方式: {'✅ 存在' if 浏览器信息['快捷方式'] else '❌ 缺失'}

创建时间: 未知
最后使用: 未知
数据大小: 计算中...

操作说明:
• 双击列表项可直接启动浏览器
• 使用右侧按钮进行各种操作
• 发送到桌面可创建快捷方式
"""
                self.info_text.insert(1.0, info)

        except Exception as e:
            print(f"显示浏览器信息失败: {e}")

    def _新建浏览器实例(self):
        """新建浏览器实例对话框"""
        dialog = 新建浏览器对话框(self.root, self.浏览器管理器实例)
        if dialog.result:
            self._刷新浏览器列表()
            self.status_label.config(text=f"已创建浏览器实例: {dialog.result}")

    def _启动选中浏览器(self):
        """启动选中的浏览器"""
        try:
            selection = self.tree.selection()
            if not selection:
                messagebox.showwarning("提示", "请先选择一个浏览器实例")
                return

            item = self.tree.item(selection[0])
            浏览器名称 = item['values'][0]

            # 在后台线程中启动浏览器
            def 启动():
                成功 = self.浏览器管理器实例.启动浏览器(浏览器名称)
                if 成功:
                    self.status_label.config(text=f"已启动浏览器: {浏览器名称}")
                else:
                    messagebox.showerror("错误", f"启动浏览器失败: {浏览器名称}")

            threading.Thread(target=启动, daemon=True).start()

        except Exception as e:
            messagebox.showerror("错误", f"启动浏览器失败: {e}")

    def _发送到桌面(self):
        """发送选中浏览器到桌面"""
        try:
            selection = self.tree.selection()
            if not selection:
                messagebox.showwarning("提示", "请先选择一个浏览器实例")
                return

            item = self.tree.item(selection[0])
            浏览器名称 = item['values'][0]

            成功 = self.浏览器管理器实例.发送到桌面(浏览器名称)
            if 成功:
                messagebox.showinfo("成功", f"已将 '{浏览器名称}' 发送到桌面")
                self.status_label.config(text=f"已发送到桌面: {浏览器名称}")
            else:
                messagebox.showerror("错误", f"发送到桌面失败: {浏览器名称}")

        except Exception as e:
            messagebox.showerror("错误", f"发送到桌面失败: {e}")

    def _删除选中浏览器(self):
        """删除选中的浏览器"""
        try:
            selection = self.tree.selection()
            if not selection:
                messagebox.showwarning("提示", "请先选择一个浏览器实例")
                return

            item = self.tree.item(selection[0])
            浏览器名称 = item['values'][0]

            # 确认删除
            if messagebox.askyesno("确认删除",
                                 f"确定要删除浏览器实例 '{浏览器名称}' 吗？\n\n此操作将删除所有相关数据，无法恢复！"):
                成功 = self.浏览器管理器实例.删除浏览器实例(浏览器名称, 需要确认=False)
                if 成功:
                    self._刷新浏览器列表()
                    self.status_label.config(text=f"已删除浏览器实例: {浏览器名称}")
                else:
                    messagebox.showerror("错误", f"删除浏览器实例失败: {浏览器名称}")

        except Exception as e:
            messagebox.showerror("错误", f"删除浏览器实例失败: {e}")

    def _双击启动浏览器(self, event):
        """双击启动浏览器"""
        self._启动选中浏览器()

    def _打开浏览器目录(self):
        """打开浏览器实例目录"""
        try:
            selection = self.tree.selection()
            if not selection:
                messagebox.showwarning("提示", "请先选择一个浏览器实例")
                return

            item = self.tree.item(selection[0])
            浏览器名称 = item['values'][0]

            实例目录 = self.浏览器管理器实例.浏览器实例目录 / 浏览器名称
            if 实例目录.exists():
                import subprocess
                subprocess.run(['explorer', str(实例目录)])
                self.status_label.config(text=f"已打开目录: {浏览器名称}")
            else:
                messagebox.showerror("错误", "浏览器实例目录不存在")

        except Exception as e:
            messagebox.showerror("错误", f"打开目录失败: {e}")

    # 菜单功能的占位符方法
    def _导入配置(self):
        messagebox.showinfo("提示", "导入配置功能开发中...")

    def _导出配置(self):
        messagebox.showinfo("提示", "导出配置功能开发中...")

    def _批量创建(self):
        messagebox.showinfo("提示", "批量创建功能开发中...")

    def _清理临时文件(self):
        messagebox.showinfo("提示", "清理临时文件功能开发中...")

    def _检查更新(self):
        messagebox.showinfo("提示", "检查更新功能开发中...")

    def _打开设置(self):
        messagebox.showinfo("提示", "设置功能开发中...")

    def _显示使用说明(self):
        messagebox.showinfo("使用说明", """浏览器多账号绿色版使用说明

1. 新建浏览器实例：点击"新建"按钮创建新的浏览器实例
2. 启动浏览器：双击列表项或点击"启动"按钮
3. 发送到桌面：创建桌面快捷方式
4. 删除实例：删除不需要的浏览器实例

特色功能：
• 真正跨电脑使用 - 整个文件夹复制即可
• 多账号完全隔离 - 每个浏览器有独立数据
• 自定义图标支持 - 个性化浏览器图标
• 一键批量配置 - Python脚本自动化配置

更多信息请查看README.md文件""")

    def _显示关于(self):
        messagebox.showinfo("关于", """浏览器多账号绿色版 v2.2.1

基于Chrome Portable的多账号浏览器管理系统

开发团队：浏览器多账号绿色版团队
技术架构：Python + Tkinter + Chrome Portable
许可证：MIT License

© 2025 All Rights Reserved""")

    def 运行(self):
        """运行GUI"""
        try:
            self.root.mainloop()
        except KeyboardInterrupt:
            print("\n👋 用户中断，程序退出")
        except Exception as e:
            print(f"❌ GUI运行时发生错误: {e}")

class 新建浏览器对话框:
    """新建浏览器实例对话框"""

    def __init__(self, parent, 浏览器管理器实例):
        self.浏览器管理器实例 = 浏览器管理器实例
        self.result = None

        # 创建对话框窗口
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("新建浏览器实例")
        self.dialog.geometry("400x300")
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)
        self.dialog.grab_set()

        # 居中显示
        self.dialog.geometry("+%d+%d" % (parent.winfo_rootx() + 50, parent.winfo_rooty() + 50))

        self._创建界面()

        # 等待对话框关闭
        self.dialog.wait_window()

    def _创建界面(self):
        """创建对话框界面"""
        main_frame = ttk.Frame(self.dialog, padding=20)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 浏览器名称
        ttk.Label(main_frame, text="浏览器名称:").pack(anchor=tk.W, pady=(0, 5))
        self.name_var = tk.StringVar()
        name_entry = ttk.Entry(main_frame, textvariable=self.name_var, width=40)
        name_entry.pack(fill=tk.X, pady=(0, 15))
        name_entry.focus()

        # 图标类型
        ttk.Label(main_frame, text="图标类型:").pack(anchor=tk.W, pady=(0, 5))
        self.icon_var = tk.StringVar(value="chrome")
        icon_frame = ttk.Frame(main_frame)
        icon_frame.pack(fill=tk.X, pady=(0, 15))

        ttk.Radiobutton(icon_frame, text="Chrome", variable=self.icon_var, value="chrome").pack(side=tk.LEFT, padx=(0, 10))
        ttk.Radiobutton(icon_frame, text="Firefox", variable=self.icon_var, value="firefox").pack(side=tk.LEFT, padx=(0, 10))
        ttk.Radiobutton(icon_frame, text="Edge", variable=self.icon_var, value="edge").pack(side=tk.LEFT, padx=(0, 10))
        ttk.Radiobutton(icon_frame, text="通用", variable=self.icon_var, value="generic").pack(side=tk.LEFT)

        # 按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=(20, 0))

        ttk.Button(button_frame, text="创建", command=self._创建浏览器).pack(side=tk.RIGHT, padx=(5, 0))
        ttk.Button(button_frame, text="取消", command=self.dialog.destroy).pack(side=tk.RIGHT)

    def _创建浏览器(self):
        """创建浏览器实例"""
        try:
            浏览器名称 = self.name_var.get().strip()
            图标类型 = self.icon_var.get()

            if not 浏览器名称:
                messagebox.showerror("错误", "请输入浏览器名称")
                return

            # 创建浏览器实例
            成功 = self.浏览器管理器实例.创建浏览器实例(浏览器名称, 图标类型)

            if 成功:
                self.result = 浏览器名称
                messagebox.showinfo("成功", f"浏览器实例 '{浏览器名称}' 创建成功！")
                self.dialog.destroy()
            else:
                messagebox.showerror("错误", "创建浏览器实例失败，请检查名称是否合法或已存在")

        except Exception as e:
            messagebox.showerror("错误", f"创建浏览器实例失败: {e}")

def main():
    """主函数"""
    try:
        print("🎨 启动图形界面...")
        gui = 浏览器管理器GUI()
        gui.运行()
    except Exception as e:
        print(f"❌ GUI启动失败: {e}")

if __name__ == "__main__":
    main()
