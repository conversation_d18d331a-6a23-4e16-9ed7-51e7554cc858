import"./modulepreload-polyfill-2ad73d06.js";import{i as e}from"./util-2d803e2a.js";import{g as t,h as i,i as n,o as s,x as a,v as l}from"./runtime-dom.esm-bundler-65457635.js";const d={class:"mac-options_page"},c=[n("div",{class:"mac-options_img"},null,-1),n("p",{class:"mac-options_content"},"扩展选项即将上线，敬请期待",-1)],o={__name:"options-mac",setup:e=>(e,n)=>(t(),i("div",d,c))},m=[a('<div class="setting-plugin" style="display:block;"><div class="setting-plugin__header"><h1>高级设置</h1></div><div class="setting-plugin__body"><ul><li><span class="title">在线视频检测</span><div class="form"><label class="checkbox"><input id="take-over-video-switch" type="checkbox" class="checkbox__inner"><span class="checkbox__label">开启</span></label></div></li><li><span class="title">批量下载快捷键</span><div class="form"><label class="checkbox"><input id="multi-sel-switch" type="checkbox" class="checkbox__inner" checked><span class="checkbox__label">开启</span></label><input class="input is-disabled" disabled type="text" value="Shift+D"></div></li><li id="monitor-exts-li"><span class="title">接管文件类型</span><div class="type-box"><textarea id="monitor-exts-textarea" class="textarea" disabled></textarea><button id="edit-monitor-exts-btn" class="button-edit">编辑</button><button style="display:none;" id="edit-cancel-btn" class="button-cencel">取消</button></div></li><li><span class="title">反馈建议</span><p class="text">对这个插件不满意吗？点击<a id="feed-back" href="javascript:;">这里</a>给我们一些建议吧。</p></li></ul></div><div class="setting-plugin__footer"><a href="https://www.xunlei.com" target="_ablank" title="迅雷"></a></div></div>',1)];l(e?o:{__name:"options-win",setup(e){let n=(...e)=>{chrome.runtime.sendMessage({name:"xl_call_function",method:"setMonitorVideoTags",args:e})},a=()=>{chrome.runtime.sendMessage({name:"xl_call_function",method:"onFeedback"})},l=(...e)=>{chrome.runtime.sendMessage({name:"xl_call_function",method:"setMultiSelectShortcutEnable",args:e})},d=(...e)=>{chrome.runtime.sendMessage({name:"xl_call_function",method:"setLimitSizeInfo",args:e})},c=async(...e)=>new Promise(e=>{chrome.runtime.sendMessage({name:"xl_call_function",method:"getLimitSizeInfo"},t=>{e(t)})}),o=async()=>new Promise(e=>{chrome.runtime.sendMessage({name:"xl_call_function",method:"getMonitorFileExts"},t=>{e(t)})}),r=async()=>new Promise(e=>{chrome.runtime.sendMessage({name:"xl_call_function",method:"isException"},t=>{e(t)})}),u=async()=>new Promise(e=>{chrome.runtime.sendMessage({name:"xl_call_function",method:"isPluginEnabled"},t=>{e(t)})}),b=async()=>new Promise(e=>{chrome.runtime.sendMessage({name:"xl_call_function",method:"isMultiSelectShortcutEnable"},t=>{e(t)})}),h=async()=>new Promise(e=>{chrome.runtime.sendMessage({name:"xl_call_function",method:"isVideoMonitor"},t=>{e(t)})}),p=(...e)=>{chrome.runtime.sendMessage({name:"xl_call_function",method:"setMonitorFileExts",args:e})},g=!1,v=!1,x=!1,f=!1,_={},y="";function E(e){do{if(g)break;if(!v)break;x=!x,n(x)}while(0)}function k(e){a()}function w(e){do{if(g)break;if(!v)break;f=!f,l(f)}while(0)}function I(e){do{if(g)break;if(!v)break;_.enable=!_.enable,d(_.enable);let e=document.getElementById("limit-size-value");_.enable?e.disabled=!1:e.disabled=!0}while(0)}function M(e){do{if(g)break;if(!v)break;let t=e.target.value;t=parseInt(t,10),(isNaN(t)||t<0)&&(t=2),_.size=t,e.target.value=t,d(!0,t)}while(0)}async function L(){document.removeEventListener("DOMContentLoaded",L);let e=!1;try{g=await r(),e=!0}catch(t){}e?async function(){chrome.runtime.onMessage.addListener((e,t,i)=>{if("UpdatePluginEnabled"==e.name)g=e.exception,v=e.enable,B();else if("UpdateMonitorFileExts"===e.name){let t=document.getElementById("monitor-exts-textarea");t.disabled&&(t.value=e.value),y=e.value}}),v=await u(),B(),x=await h();let e=document.getElementById("take-over-video-switch");e.checked=x,f=await b();let t=document.getElementById("multi-sel-switch");t.checked=f;{document.getElementById("monitor-exts-li");let e=document.getElementById("edit-monitor-exts-btn"),t=document.getElementById("monitor-exts-textarea"),i=document.getElementById("edit-cancel-btn");y=await o(),t.value=y,e.addEventListener("click",()=>{"编辑"===e.innerText?(e.innerText="确定",t.disabled=!1,i.style.display=""):(e.innerText="编辑",t.disabled=!0,i.style.display="none",p(t.value||""))}),i.addEventListener("click",()=>{e.innerText="编辑",t.disabled=!0,i.style.display="none",g||(t.value=y)})}document.getElementById("feed-back").addEventListener("click",k),e.addEventListener("click",E),t.addEventListener("click",w);let i=document.getElementById("limit-size-switch");if(i){_=await c()||{enable:!1,size:2},i.checked=_.enable;let e=document.getElementById("limit-size-value");e.disabled&&e.classList.add("is-disabled"),e.value=_.size,i.addEventListener("click",I),e.addEventListener("blur",M)}}():setTimeout(()=>{L()},50)}async function B(){let e=g||!v,t=document.getElementById("take-over-video-switch");t.disabled=e,e?t.parentElement.classList.add("is-disabled"):t.parentElement.classList.remove("is-disabled");let i=document.getElementById("multi-sel-switch");i.disabled=e,e?i.parentElement.classList.add("is-disabled"):i.parentElement.classList.remove("is-disabled");let n=document.getElementById("edit-monitor-exts-btn");n.disabled=e,e?n.classList.add("is-disabled"):n.classList.remove("is-disabled");let s=document.getElementById("monitor-exts-textarea");s.disabled=e||"编辑"===n.innerText;let a=document.getElementById("limit-size-switch");if(a){a.disabled=e;let t=document.getElementById("limit-size-value");t.disabled=e||!(await c()||{enable:!1,size:2}).enable}}return s(()=>{L()}),(e,n)=>(t(),i("div",null,m))}}).mount("#app");
