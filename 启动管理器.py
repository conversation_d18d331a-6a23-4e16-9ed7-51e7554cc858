#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
浏览器多账号绿色版 - 启动管理器
版本: v2.2.1
作者: 浏览器多账号绿色版团队
描述: 系统启动入口，负责环境检查、依赖验证和程序启动
"""

import sys
import os
import subprocess
import platform
from pathlib import Path
import importlib.util

def 检查Python版本():
    """检查Python版本是否符合要求"""
    print("🐍 检查Python环境...")
    
    版本信息 = sys.version_info
    当前版本 = f"{版本信息.major}.{版本信息.minor}.{版本信息.micro}"
    
    print(f"  当前Python版本: {当前版本}")
    
    # 要求Python 3.7+
    if 版本信息.major < 3 or (版本信息.major == 3 and 版本信息.minor < 7):
        print("  ❌ Python版本过低，需要Python 3.7或更高版本")
        return False
    
    print("  ✅ Python版本符合要求")
    return True

def 检查依赖包():
    """检查必要的依赖包"""
    print("\n📦 检查依赖包...")
    
    必要依赖 = [
        ('json', 'JSON处理'),
        ('tkinter', 'GUI界面'),
        ('pathlib', '路径处理'),
        ('subprocess', '进程管理')
    ]
    
    可选依赖 = [
        ('win32com.client', 'Windows COM组件'),
        ('requests', '网络请求'),
        ('PIL', '图像处理')
    ]
    
    # 检查必要依赖
    缺失依赖 = []
    for 包名, 描述 in 必要依赖:
        try:
            __import__(包名)
            print(f"  ✅ {描述} ({包名})")
        except ImportError:
            print(f"  ❌ {描述} ({包名}) - 缺失")
            缺失依赖.append(包名)
    
    # 检查可选依赖
    print("\n📦 检查可选依赖...")
    for 包名, 描述 in 可选依赖:
        try:
            if 包名 == 'win32com.client':
                import win32com.client
            elif 包名 == 'requests':
                import requests
            elif 包名 == 'PIL':
                from PIL import Image
            print(f"  ✅ {描述} ({包名})")
        except ImportError:
            print(f"  ⚠️ {描述} ({包名}) - 未安装（可选）")
    
    if 缺失依赖:
        print(f"\n❌ 发现缺失的必要依赖: {', '.join(缺失依赖)}")
        return False
    
    print("\n✅ 所有必要依赖检查通过")
    return True

def 安装依赖():
    """安装缺失的依赖"""
    print("\n🔧 尝试安装依赖包...")
    
    try:
        # 检查requirements.txt是否存在
        requirements_file = Path("requirements.txt")
        if not requirements_file.exists():
            print("  ❌ requirements.txt文件不存在")
            return False
        
        # 使用pip安装依赖
        print("  📥 正在安装依赖包...")
        result = subprocess.run([
            sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("  ✅ 依赖包安装成功")
            return True
        else:
            print(f"  ❌ 依赖包安装失败: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"  ❌ 安装过程中发生错误: {e}")
        return False

def 检查配置文件():
    """检查配置文件是否存在"""
    print("\n⚙️ 检查配置文件...")
    
    配置文件列表 = [
        ("项目配置.json", "项目配置文件"),
        ("思维导图.json", "思维导图配置文件")
    ]
    
    缺失文件 = []
    for 文件名, 描述 in 配置文件列表:
        文件路径 = Path(文件名)
        if 文件路径.exists():
            print(f"  ✅ {描述}")
        else:
            print(f"  ❌ {描述} - 缺失")
            缺失文件.append(文件名)
    
    if 缺失文件:
        print(f"\n❌ 发现缺失的配置文件: {', '.join(缺失文件)}")
        return False
    
    print("\n✅ 配置文件检查通过")
    return True

def 检查Chrome程序():
    """检查Chrome Portable程序"""
    print("\n🌐 检查Chrome Portable程序...")
    
    chrome_path = Path("GoogleChromePortable/GoogleChromePortable.exe")
    if chrome_path.exists():
        print(f"  ✅ Chrome Portable程序存在: {chrome_path}")
        return True
    else:
        print(f"  ❌ Chrome Portable程序不存在: {chrome_path}")
        return False

def 初始化目录结构():
    """初始化必要的目录结构"""
    print("\n📁 初始化目录结构...")
    
    必要目录 = [
        "浏览器实例",
        "备份",
        "日志",
        "temp"
    ]
    
    for 目录名 in 必要目录:
        目录路径 = Path(目录名)
        if not 目录路径.exists():
            目录路径.mkdir(exist_ok=True)
            print(f"  ✅ 创建目录: {目录名}")
        else:
            print(f"  ✅ 目录已存在: {目录名}")
    
    return True

def 启动图形界面():
    """启动图形界面"""
    print("\n🖥️ 启动图形界面...")
    
    try:
        # 检查GUI文件是否存在
        gui_file = Path("浏览器管理器GUI.py")
        if not gui_file.exists():
            print("  ❌ 浏览器管理器GUI.py文件不存在")
            print("  💡 将启动命令行版本...")
            return 启动命令行界面()
        
        # 启动GUI
        import subprocess
        result = subprocess.run([sys.executable, "浏览器管理器GUI.py"], 
                              capture_output=False)
        return result.returncode == 0
        
    except Exception as e:
        print(f"  ❌ 启动图形界面失败: {e}")
        print("  💡 将启动命令行版本...")
        return 启动命令行界面()

def 启动命令行界面():
    """启动命令行界面"""
    print("\n⌨️ 启动命令行界面...")
    
    try:
        # 检查命令行文件是否存在
        cli_file = Path("浏览器管理器.py")
        if not cli_file.exists():
            print("  ❌ 浏览器管理器.py文件不存在")
            print("  💡 请先创建浏览器管理器模块")
            return False
        
        # 启动命令行版本
        import subprocess
        result = subprocess.run([sys.executable, "浏览器管理器.py"], 
                              capture_output=False)
        return result.returncode == 0
        
    except Exception as e:
        print(f"  ❌ 启动命令行界面失败: {e}")
        return False

def 显示帮助信息():
    """显示帮助信息"""
    print("""
🌟 浏览器多账号绿色版 v2.2.1

📖 使用方法:
  python 启动管理器.py          # 自动启动（推荐）
  python 启动管理器.py --gui    # 强制启动图形界面
  python 启动管理器.py --cli    # 强制启动命令行界面
  python 启动管理器.py --check  # 仅进行环境检查
  python 启动管理器.py --help   # 显示帮助信息

🎯 功能特性:
  ✅ 真正跨电脑使用 - 整个文件夹复制即可
  ✅ 多账号完全隔离 - 每个浏览器有独立数据
  ✅ 自定义图标支持 - 个性化浏览器图标
  ✅ 一键批量配置 - Python脚本自动化配置

📚 更多信息请查看 README.md 文件
""")

def main():
    """主函数"""
    print("🌟 浏览器多账号绿色版 v2.2.1")
    print("=" * 50)
    
    # 解析命令行参数
    参数 = sys.argv[1:] if len(sys.argv) > 1 else []
    
    if "--help" in 参数 or "-h" in 参数:
        显示帮助信息()
        return
    
    if "--check" in 参数:
        print("🔍 仅进行环境检查...")
        环境检查通过 = 进行环境检查()
        if 环境检查通过:
            print("\n🎉 环境检查全部通过！系统可以正常运行。")
        else:
            print("\n❌ 环境检查发现问题，请解决后重试。")
        return
    
    # 进行完整的环境检查
    环境检查通过 = 进行环境检查()
    
    if not 环境检查通过:
        print("\n❌ 环境检查失败，无法启动程序")
        print("💡 请运行 'python 启动管理器.py --check' 查看详细信息")
        return
    
    print("\n🎉 环境检查通过！正在启动程序...")
    
    # 根据参数选择启动方式
    if "--gui" in 参数:
        启动成功 = 启动图形界面()
    elif "--cli" in 参数:
        启动成功 = 启动命令行界面()
    else:
        # 自动选择启动方式（优先GUI）
        启动成功 = 启动图形界面()
    
    if 启动成功:
        print("\n✅ 程序启动成功")
    else:
        print("\n❌ 程序启动失败")

def 进行环境检查():
    """进行完整的环境检查"""
    检查项目 = [
        ("Python版本", 检查Python版本),
        ("依赖包", 检查依赖包),
        ("配置文件", 检查配置文件),
        ("Chrome程序", 检查Chrome程序),
        ("目录结构", 初始化目录结构)
    ]
    
    所有检查通过 = True
    
    for 项目名, 检查函数 in 检查项目:
        try:
            结果 = 检查函数()
            if not 结果:
                所有检查通过 = False
        except Exception as e:
            print(f"❌ {项目名}检查时发生错误: {e}")
            所有检查通过 = False
    
    return 所有检查通过

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 用户中断，程序退出")
    except Exception as e:
        print(f"\n❌ 程序运行时发生错误: {e}")
        print("💡 请检查环境配置或联系技术支持")
