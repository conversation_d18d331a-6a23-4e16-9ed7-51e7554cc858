{"appDescription": {"_message.comment": "{MaxLength=132}", "message": "Add-on for enabling web automation. This web extension is compatible with Power Automate for desktop version 2.27 or later."}, "appName": {"message": "Microsoft Power Automate"}, "consentAccept": {"message": "Accept"}, "consentCollectedData1": {"message": "The uri of the web page that you interact which we collect for enabling the web automation capabilities of Power Automate for desktop."}, "consentDecline": {"message": "Decline"}, "consentHeader": {"message": "Microsoft Power Automate Privacy consent"}, "consentParagraph1": {"message": "If you choose not to allow us to collect anonymous data then web automation will not be available."}, "consentParagraph2": {"message": "Full details about the anonymous data we collect and what we do with it are provided in our "}, "consentPrivacyLink": {"message": "Privacy Policy"}, "consentQuestion": {"message": "Can we collect the following anonymous data about your use of Microsoft Power Automate web extension?"}, "consentUninstall": {"message": "This will uninstall the Microsoft Power Automate web extension. Do you want to continue?"}}