#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
浏览器多账号绿色版 - 功能测试脚本
版本: v2.2.1
作者: 浏览器多账号绿色版团队
描述: 自动化测试所有核心功能，确保系统正常工作
"""

import sys
import os
import time
import shutil
from pathlib import Path
from 配置管理器 import 配置管理器
from 浏览器管理器 import 浏览器管理器

class 功能测试器:
    """功能测试器"""
    
    def __init__(self):
        """初始化测试器"""
        self.测试结果 = {}
        self.配置器 = None
        self.浏览器管理器实例 = None
        self.测试浏览器名称 = "自动测试浏览器"
        
        print("🧪 功能测试器初始化...")
    
    def 运行所有测试(self):
        """运行所有测试"""
        print("🚀 开始运行所有功能测试...")
        print("=" * 60)
        
        测试项目 = [
            ("配置管理器测试", self.测试配置管理器),
            ("浏览器管理器初始化测试", self.测试浏览器管理器初始化),
            ("浏览器实例创建测试", self.测试浏览器实例创建),
            ("浏览器列表查看测试", self.测试浏览器列表查看),
            ("浏览器启动测试", self.测试浏览器启动),
            ("浏览器删除测试", self.测试浏览器删除),
            ("环境完整性测试", self.测试环境完整性)
        ]
        
        总测试数 = len(测试项目)
        通过测试数 = 0
        
        for i, (测试名称, 测试函数) in enumerate(测试项目, 1):
            print(f"\n📋 [{i}/{总测试数}] {测试名称}")
            print("-" * 40)
            
            try:
                结果 = 测试函数()
                self.测试结果[测试名称] = 结果
                
                if 结果:
                    print(f"✅ {测试名称} - 通过")
                    通过测试数 += 1
                else:
                    print(f"❌ {测试名称} - 失败")
                    
            except Exception as e:
                print(f"❌ {测试名称} - 异常: {e}")
                self.测试结果[测试名称] = False
        
        # 显示测试总结
        self.显示测试总结(通过测试数, 总测试数)
        
        return 通过测试数 == 总测试数
    
    def 测试配置管理器(self):
        """测试配置管理器功能"""
        try:
            # 创建配置管理器实例
            self.配置器 = 配置管理器()
            
            # 测试配置读取
            项目名称 = self.配置器.获取项目配置('项目信息.名称')
            版本号 = self.配置器.获取项目配置('项目信息.版本')
            
            if not 项目名称 or not 版本号:
                print("  ❌ 配置读取失败")
                return False
            
            print(f"  ✅ 项目名称: {项目名称}")
            print(f"  ✅ 版本号: {版本号}")
            
            # 测试路径配置
            chrome_path = self.配置器.获取Chrome路径()
            if not chrome_path or not chrome_path.exists():
                print("  ❌ Chrome路径配置错误")
                return False
            
            print(f"  ✅ Chrome路径: {chrome_path}")
            
            # 测试配置完整性
            完整性结果 = self.配置器.检查配置完整性()
            失败项目 = [项目 for 项目, 状态 in 完整性结果.items() if not 状态]
            
            if 失败项目:
                print(f"  ❌ 配置完整性检查失败: {失败项目}")
                return False
            
            print("  ✅ 配置完整性检查通过")
            return True
            
        except Exception as e:
            print(f"  ❌ 配置管理器测试异常: {e}")
            return False
    
    def 测试浏览器管理器初始化(self):
        """测试浏览器管理器初始化"""
        try:
            self.浏览器管理器实例 = 浏览器管理器()
            
            # 检查必要属性
            if not hasattr(self.浏览器管理器实例, '配置器'):
                print("  ❌ 配置器属性缺失")
                return False
            
            if not hasattr(self.浏览器管理器实例, '浏览器实例目录'):
                print("  ❌ 浏览器实例目录属性缺失")
                return False
            
            # 检查目录是否存在
            if not self.浏览器管理器实例.浏览器实例目录.exists():
                print("  ❌ 浏览器实例目录不存在")
                return False
            
            print("  ✅ 浏览器管理器初始化成功")
            return True
            
        except Exception as e:
            print(f"  ❌ 浏览器管理器初始化异常: {e}")
            return False
    
    def 测试浏览器实例创建(self):
        """测试浏览器实例创建"""
        try:
            if not self.浏览器管理器实例:
                print("  ❌ 浏览器管理器未初始化")
                return False
            
            # 清理可能存在的测试浏览器
            self._清理测试浏览器()
            
            # 创建测试浏览器实例
            创建结果 = self.浏览器管理器实例.创建浏览器实例(self.测试浏览器名称, "chrome")
            
            if not 创建结果:
                print("  ❌ 浏览器实例创建失败")
                return False
            
            # 验证创建结果
            实例目录 = self.浏览器管理器实例.浏览器实例目录 / self.测试浏览器名称
            
            if not 实例目录.exists():
                print("  ❌ 实例目录未创建")
                return False
            
            chrome_bin = 实例目录 / "Chrome-bin"
            if not chrome_bin.exists():
                print("  ❌ Chrome程序未复制")
                return False
            
            数据目录 = 实例目录 / f"Data_{self.测试浏览器名称}"
            if not 数据目录.exists():
                print("  ❌ 数据目录未创建")
                return False
            
            print("  ✅ 浏览器实例创建成功")
            print(f"  ✅ 实例目录: {实例目录}")
            print(f"  ✅ Chrome程序: {chrome_bin}")
            print(f"  ✅ 数据目录: {数据目录}")
            
            return True
            
        except Exception as e:
            print(f"  ❌ 浏览器实例创建测试异常: {e}")
            return False
    
    def 测试浏览器列表查看(self):
        """测试浏览器列表查看"""
        try:
            if not self.浏览器管理器实例:
                print("  ❌ 浏览器管理器未初始化")
                return False
            
            浏览器列表 = self.浏览器管理器实例.获取浏览器列表()
            
            if not 浏览器列表:
                print("  ❌ 浏览器列表为空")
                return False
            
            # 查找测试浏览器
            测试浏览器 = None
            for 浏览器 in 浏览器列表:
                if 浏览器['名称'] == self.测试浏览器名称:
                    测试浏览器 = 浏览器
                    break
            
            if not 测试浏览器:
                print(f"  ❌ 未找到测试浏览器: {self.测试浏览器名称}")
                return False
            
            # 验证浏览器信息
            必要字段 = ['名称', '路径', 'Chrome程序', '数据目录', '状态']
            for 字段 in 必要字段:
                if 字段 not in 测试浏览器:
                    print(f"  ❌ 浏览器信息缺少字段: {字段}")
                    return False
            
            if 测试浏览器['状态'] != '正常':
                print(f"  ❌ 浏览器状态异常: {测试浏览器['状态']}")
                return False
            
            print(f"  ✅ 浏览器列表查看成功，共 {len(浏览器列表)} 个实例")
            print(f"  ✅ 测试浏览器状态: {测试浏览器['状态']}")
            
            return True
            
        except Exception as e:
            print(f"  ❌ 浏览器列表查看测试异常: {e}")
            return False
    
    def 测试浏览器启动(self):
        """测试浏览器启动"""
        try:
            if not self.浏览器管理器实例:
                print("  ❌ 浏览器管理器未初始化")
                return False
            
            # 注意：这里只测试启动命令的构建，不实际启动浏览器
            # 因为实际启动会打开浏览器窗口，影响自动化测试
            
            实例目录 = self.浏览器管理器实例.浏览器实例目录 / self.测试浏览器名称
            chrome_exe = 实例目录 / "Chrome-bin" / "chrome.exe"
            数据目录 = 实例目录 / f"Data_{self.测试浏览器名称}"
            
            # 检查启动所需文件
            if not chrome_exe.exists():
                print(f"  ❌ Chrome可执行文件不存在: {chrome_exe}")
                return False
            
            if not 数据目录.exists():
                print(f"  ❌ 数据目录不存在: {数据目录}")
                return False
            
            print("  ✅ 浏览器启动条件检查通过")
            print(f"  ✅ Chrome程序: {chrome_exe}")
            print(f"  ✅ 数据目录: {数据目录}")
            print("  ℹ️ 跳过实际启动以避免打开浏览器窗口")
            
            return True
            
        except Exception as e:
            print(f"  ❌ 浏览器启动测试异常: {e}")
            return False
    
    def 测试浏览器删除(self):
        """测试浏览器删除"""
        try:
            if not self.浏览器管理器实例:
                print("  ❌ 浏览器管理器未初始化")
                return False
            
            实例目录 = self.浏览器管理器实例.浏览器实例目录 / self.测试浏览器名称
            
            if not 实例目录.exists():
                print("  ❌ 测试浏览器实例不存在")
                return False
            
            # 直接删除目录（跳过用户确认）
            shutil.rmtree(实例目录)
            
            if 实例目录.exists():
                print("  ❌ 浏览器实例删除失败")
                return False
            
            print("  ✅ 浏览器实例删除成功")
            return True
            
        except Exception as e:
            print(f"  ❌ 浏览器删除测试异常: {e}")
            return False
    
    def 测试环境完整性(self):
        """测试环境完整性"""
        try:
            # 检查Python版本
            版本信息 = sys.version_info
            if 版本信息.major < 3 or (版本信息.major == 3 and 版本信息.minor < 7):
                print("  ❌ Python版本过低")
                return False
            
            # 检查必要模块
            必要模块 = ['json', 'pathlib', 'subprocess', 'shutil']
            for 模块 in 必要模块:
                try:
                    __import__(模块)
                except ImportError:
                    print(f"  ❌ 缺少必要模块: {模块}")
                    return False
            
            # 检查可选模块
            可选模块 = ['win32com.client', 'requests', 'PIL']
            缺失可选模块 = []
            for 模块 in 可选模块:
                try:
                    __import__(模块)
                except ImportError:
                    缺失可选模块.append(模块)
            
            if 缺失可选模块:
                print(f"  ⚠️ 缺少可选模块: {', '.join(缺失可选模块)}")
            
            # 检查文件结构
            必要文件 = [
                "配置管理器.py",
                "浏览器管理器.py",
                "启动管理器.py",
                "项目配置.json",
                "思维导图.json",
                "requirements.txt"
            ]
            
            缺失文件 = []
            for 文件 in 必要文件:
                if not Path(文件).exists():
                    缺失文件.append(文件)
            
            if 缺失文件:
                print(f"  ❌ 缺少必要文件: {', '.join(缺失文件)}")
                return False
            
            print("  ✅ 环境完整性检查通过")
            print(f"  ✅ Python版本: {版本信息.major}.{版本信息.minor}.{版本信息.micro}")
            print("  ✅ 所有必要文件存在")
            
            return True
            
        except Exception as e:
            print(f"  ❌ 环境完整性测试异常: {e}")
            return False
    
    def _清理测试浏览器(self):
        """清理测试浏览器"""
        try:
            if self.浏览器管理器实例:
                实例目录 = self.浏览器管理器实例.浏览器实例目录 / self.测试浏览器名称
                if 实例目录.exists():
                    shutil.rmtree(实例目录)
        except Exception:
            pass  # 忽略清理错误
    
    def 显示测试总结(self, 通过数, 总数):
        """显示测试总结"""
        print("\n" + "=" * 60)
        print("🎯 测试总结")
        print("=" * 60)
        
        for 测试名称, 结果 in self.测试结果.items():
            状态 = "✅ 通过" if 结果 else "❌ 失败"
            print(f"  {状态} {测试名称}")
        
        成功率 = (通过数 / 总数) * 100 if 总数 > 0 else 0
        
        print(f"\n📊 测试统计:")
        print(f"  总测试数: {总数}")
        print(f"  通过数: {通过数}")
        print(f"  失败数: {总数 - 通过数}")
        print(f"  成功率: {成功率:.1f}%")
        
        if 通过数 == 总数:
            print("\n🎉 所有测试通过！系统功能正常。")
        else:
            print(f"\n⚠️ 有 {总数 - 通过数} 个测试失败，请检查相关功能。")

def main():
    """主函数"""
    print("🧪 浏览器多账号绿色版 - 功能测试")
    print("版本: v2.2.1")
    print("=" * 60)
    
    try:
        测试器 = 功能测试器()
        测试成功 = 测试器.运行所有测试()
        
        if 测试成功:
            print("\n✅ 所有功能测试通过，系统可以正常使用！")
            return 0
        else:
            print("\n❌ 部分功能测试失败，请检查系统配置！")
            return 1
            
    except KeyboardInterrupt:
        print("\n\n👋 用户中断测试")
        return 1
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
