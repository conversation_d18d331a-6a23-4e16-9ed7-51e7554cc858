graph TD
    A[🎯 浏览器多账号绿色版 - 核心角色定义] --> B[🔧 浏览器架构师]
    A --> C[🎨 用户体验设计师]
    A --> D[📊 数据管理专家]
    A --> E[🔒 安全技术专家]
    A --> F[📚 文档管理专家]
    A --> G[🚀 产品迭代专家]

    B --> B1[🏗️ 核心职责]
    B --> B2[🔧 技术能力]
    B --> B3[📋 工作范围]
    B --> B4[🎯 交付成果]

    B1 --> B11[Chrome Portable集成架构]
    B1 --> B12[多实例隔离设计]
    B1 --> B13[相对路径绑定技术]
    B1 --> B14[跨平台兼容性保证]

    B2 --> B21[Python系统编程]
    B2 --> B22[Windows COM组件]
    B2 --> B23[文件系统操作]
    B2 --> B24[进程管理技术]

    B3 --> B31[浏览器实例管理]
    B3 --> B32[快捷方式创建]
    B3 --> B33[数据目录管理]
    B3 --> B34[程序启动控制]

    B4 --> B41[启动管理器.py]
    B4 --> B42[浏览器管理器.py]
    B4 --> B43[快捷方式管理器.py]
    B4 --> B44[架构设计文档]

    C --> C1[🎨 核心职责]
    C --> C2[🖥️ 界面技能]
    C --> C3[📋 工作范围]
    C --> C4[🎯 交付成果]

    C1 --> C11[图形界面设计]
    C1 --> C12[用户交互优化]
    C1 --> C13[主题管理系统]
    C1 --> C14[一键启动体验]

    C2 --> C21[Tkinter GUI开发]
    C2 --> C22[主题配色设计]
    C2 --> C23[用户体验设计]
    C2 --> C24[界面响应优化]

    C3 --> C31[GUI界面开发]
    C3 --> C32[主题切换功能]
    C3 --> C33[图标选择器]
    C3 --> C34[设置对话框]

    C4 --> C41[浏览器管理器GUI.py]
    C4 --> C42[主题管理器.py]
    C4 --> C43[图标选择器.py]
    C4 --> C44[用户体验规范]

    D --> D1[📊 核心职责]
    D --> D2[🔧 技术能力]
    D --> D3[📋 工作范围]
    D --> D4[🎯 交付成果]

    D1 --> D11[配置管理系统]
    D1 --> D12[插件同步算法]
    D1 --> D13[数据隔离机制]
    D1 --> D14[版本管理体系]

    D2 --> D21[JSON配置管理]
    D2 --> D22[文件操作技术]
    D2 --> D23[数据同步算法]
    D2 --> D24[备份恢复机制]

    D3 --> D31[配置文件管理]
    D3 --> D32[插件数据同步]
    D3 --> D33[用户数据备份]
    D3 --> D34[版本控制管理]

    D4 --> D41[配置管理器.py]
    D4 --> D42[插件同步管理器.py]
    D4 --> D43[数据备份器.py]
    D4 --> D44[配置规范文档]

    E --> E1[🔒 核心职责]
    E --> E2[🛡️ 安全技能]
    E --> E3[📋 工作范围]
    E --> E4[🎯 交付成果]

    E1 --> E11[用户数据隔离]
    E1 --> E12[指纹保护技术]
    E1 --> E13[代理配置管理]
    E1 --> E14[隐私保护机制]

    E2 --> E21[数据加密技术]
    E2 --> E22[指纹伪装技术]
    E2 --> E23[网络代理配置]
    E2 --> E24[隐私保护策略]

    E3 --> E31[数据安全管理]
    E3 --> E32[指纹配置生成]
    E3 --> E33[代理设置管理]
    E3 --> E34[隐私策略实施]

    E4 --> E41[安全管理器.py]
    E4 --> E42[指纹生成器.py]
    E4 --> E43[代理配置器.py]
    E4 --> E44[安全规范文档]

    F --> F1[📚 核心职责]
    F --> F2[✍️ 文档技能]
    F --> F3[📋 工作范围]
    F --> F4[🎯 交付成果]

    F1 --> F11[思维导图系统]
    F1 --> F12[API文档管理]
    F1 --> F13[用户指南编写]
    F1 --> F14[版本发布说明]

    F2 --> F21[Markdown文档编写]
    F2 --> F22[Mermaid图表绘制]
    F2 --> F23[技术文档规范]
    F2 --> F24[用户体验文档]

    F3 --> F31[项目文档体系]
    F3 --> F32[思维导图管理]
    F3 --> F33[API接口文档]
    F3 --> F34[用户使用指南]

    F4 --> F41[README.md完善]
    F4 --> F42[思维导图系统]
    F4 --> F43[API文档体系]
    F4 --> F44[版本发布文档]

    G --> G1[🚀 核心职责]
    G --> G2[📈 产品技能]
    G --> G3[📋 工作范围]
    G --> G4[🎯 交付成果]

    G1 --> G11[功能需求分析]
    G1 --> G12[版本规划管理]
    G1 --> G13[用户反馈处理]
    G1 --> G14[技术债务管理]

    G2 --> G21[产品规划能力]
    G2 --> G22[需求分析技能]
    G2 --> G23[版本管理经验]
    G2 --> G24[用户体验洞察]

    G3 --> G31[产品功能规划]
    G3 --> G32[版本迭代管理]
    G3 --> G33[用户需求收集]
    G3 --> G34[技术方案评估]

    G4 --> G41[产品规划文档]
    G4 --> G42[版本发布计划]
    G4 --> G43[需求分析报告]
    G4 --> G44[技术路线图]

    style A fill:#4CAF50,color:#fff
    style B fill:#2196F3,color:#fff
    style C fill:#FF9800,color:#fff
    style D fill:#9C27B0,color:#fff
    style E fill:#F44336,color:#fff
    style F fill:#607D8B,color:#fff
    style G fill:#E91E63,color:#fff
