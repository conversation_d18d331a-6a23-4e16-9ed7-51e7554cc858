#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
浏览器多账号绿色版 - 新建删除功能测试
版本: v2.2.1
作者: 浏览器多账号绿色版团队
描述: 专门测试新建和删除功能的异常修复
"""

import sys
import shutil
from pathlib import Path
from 浏览器管理器 import 浏览器管理器

def 测试新建功能():
    """测试新建浏览器实例功能"""
    print("🧪 测试新建浏览器实例功能...")
    
    try:
        管理器 = 浏览器管理器()
        测试名称 = "新建功能测试浏览器"
        
        # 清理可能存在的测试实例
        实例目录 = 管理器.浏览器实例目录 / 测试名称
        if 实例目录.exists():
            shutil.rmtree(实例目录)
            print(f"  ✅ 清理已存在的测试实例: {测试名称}")
        
        # 测试创建浏览器实例
        print(f"  📝 创建浏览器实例: {测试名称}")
        创建成功 = 管理器.创建浏览器实例(测试名称, "chrome")
        
        if 创建成功:
            print(f"  ✅ 浏览器实例创建成功: {测试名称}")
            
            # 验证创建结果
            if 实例目录.exists():
                print(f"  ✅ 实例目录已创建: {实例目录}")
                
                # 检查必要文件
                chrome_bin = 实例目录 / "Chrome-bin"
                数据目录 = 实例目录 / f"Data_{测试名称}"
                
                if chrome_bin.exists():
                    print("  ✅ Chrome程序已复制")
                else:
                    print("  ❌ Chrome程序未复制")
                    return False
                
                if 数据目录.exists():
                    print("  ✅ 数据目录已创建")
                else:
                    print("  ❌ 数据目录未创建")
                    return False
                
                return True
            else:
                print(f"  ❌ 实例目录未创建: {实例目录}")
                return False
        else:
            print(f"  ❌ 浏览器实例创建失败: {测试名称}")
            return False
            
    except Exception as e:
        print(f"  ❌ 新建功能测试异常: {e}")
        return False

def 测试删除功能_命令行模式():
    """测试删除功能（命令行模式）"""
    print("\n🧪 测试删除功能（命令行模式）...")
    
    try:
        管理器 = 浏览器管理器()
        测试名称 = "删除功能测试浏览器_CLI"
        
        # 先创建一个测试实例
        print(f"  📝 创建测试实例: {测试名称}")
        创建成功 = 管理器.创建浏览器实例(测试名称, "firefox")
        
        if not 创建成功:
            print(f"  ❌ 测试实例创建失败: {测试名称}")
            return False
        
        实例目录 = 管理器.浏览器实例目录 / 测试名称
        
        # 测试删除功能（需要确认=False，模拟自动确认）
        print(f"  🗑️ 删除测试实例: {测试名称}")
        删除成功 = 管理器.删除浏览器实例(测试名称, 需要确认=False)
        
        if 删除成功:
            print(f"  ✅ 浏览器实例删除成功: {测试名称}")
            
            # 验证删除结果
            if not 实例目录.exists():
                print(f"  ✅ 实例目录已删除: {实例目录}")
                return True
            else:
                print(f"  ❌ 实例目录仍然存在: {实例目录}")
                return False
        else:
            print(f"  ❌ 浏览器实例删除失败: {测试名称}")
            return False
            
    except Exception as e:
        print(f"  ❌ 删除功能测试异常: {e}")
        return False

def 测试删除功能_GUI模式():
    """测试删除功能（GUI模式）"""
    print("\n🧪 测试删除功能（GUI模式）...")
    
    try:
        管理器 = 浏览器管理器()
        测试名称 = "删除功能测试浏览器_GUI"
        
        # 先创建一个测试实例
        print(f"  📝 创建测试实例: {测试名称}")
        创建成功 = 管理器.创建浏览器实例(测试名称, "edge")
        
        if not 创建成功:
            print(f"  ❌ 测试实例创建失败: {测试名称}")
            return False
        
        实例目录 = 管理器.浏览器实例目录 / 测试名称
        
        # 测试删除功能（GUI模式，需要确认=False）
        print(f"  🗑️ 删除测试实例（GUI模式）: {测试名称}")
        删除成功 = 管理器.删除浏览器实例(测试名称, 需要确认=False)
        
        if 删除成功:
            print(f"  ✅ GUI模式删除成功: {测试名称}")
            
            # 验证删除结果
            if not 实例目录.exists():
                print(f"  ✅ 实例目录已删除: {实例目录}")
                return True
            else:
                print(f"  ❌ 实例目录仍然存在: {实例目录}")
                return False
        else:
            print(f"  ❌ GUI模式删除失败: {测试名称}")
            return False
            
    except Exception as e:
        print(f"  ❌ GUI模式删除测试异常: {e}")
        return False

def 测试异常情况():
    """测试异常情况处理"""
    print("\n🧪 测试异常情况处理...")
    
    try:
        管理器 = 浏览器管理器()
        
        # 测试删除不存在的浏览器
        print("  🧪 测试删除不存在的浏览器...")
        删除结果 = 管理器.删除浏览器实例("不存在的浏览器", 需要确认=False)
        if not 删除结果:
            print("  ✅ 正确处理了不存在的浏览器删除请求")
        else:
            print("  ❌ 错误地报告删除成功")
            return False
        
        # 测试创建重名浏览器
        print("  🧪 测试创建重名浏览器...")
        测试名称 = "重名测试浏览器"
        
        # 先创建一个
        创建成功1 = 管理器.创建浏览器实例(测试名称, "chrome")
        if 创建成功1:
            print(f"  ✅ 第一次创建成功: {测试名称}")
            
            # 再创建同名的
            创建成功2 = 管理器.创建浏览器实例(测试名称, "chrome")
            if not 创建成功2:
                print("  ✅ 正确拒绝了重名浏览器创建")
                
                # 清理测试实例
                管理器.删除浏览器实例(测试名称, 需要确认=False)
                print(f"  ✅ 清理测试实例: {测试名称}")
                return True
            else:
                print("  ❌ 错误地允许了重名浏览器创建")
                # 清理测试实例
                管理器.删除浏览器实例(测试名称, 需要确认=False)
                return False
        else:
            print(f"  ❌ 第一次创建失败: {测试名称}")
            return False
            
    except Exception as e:
        print(f"  ❌ 异常情况测试异常: {e}")
        return False

def 清理测试数据():
    """清理所有测试数据"""
    print("\n🧹 清理测试数据...")
    
    try:
        管理器 = 浏览器管理器()
        
        测试名称列表 = [
            "新建功能测试浏览器",
            "删除功能测试浏览器_CLI", 
            "删除功能测试浏览器_GUI",
            "重名测试浏览器"
        ]
        
        for 测试名称 in 测试名称列表:
            实例目录 = 管理器.浏览器实例目录 / 测试名称
            if 实例目录.exists():
                shutil.rmtree(实例目录)
                print(f"  ✅ 清理测试实例: {测试名称}")
        
        print("  ✅ 测试数据清理完成")
        return True
        
    except Exception as e:
        print(f"  ❌ 清理测试数据异常: {e}")
        return False

def main():
    """主函数"""
    print("🧪 浏览器多账号绿色版 - 新建删除功能测试")
    print("版本: v2.2.1")
    print("="*60)
    
    测试项目 = [
        ("新建功能测试", 测试新建功能),
        ("删除功能测试（命令行模式）", 测试删除功能_命令行模式),
        ("删除功能测试（GUI模式）", 测试删除功能_GUI模式),
        ("异常情况处理测试", 测试异常情况)
    ]
    
    测试结果 = {}
    
    try:
        for i, (测试名称, 测试函数) in enumerate(测试项目, 1):
            print(f"\n📋 [{i}/{len(测试项目)}] {测试名称}")
            print("-" * 40)
            
            try:
                结果 = 测试函数()
                测试结果[测试名称] = 结果
                
                if 结果:
                    print(f"✅ {测试名称} - 通过")
                else:
                    print(f"❌ {测试名称} - 失败")
                    
            except Exception as e:
                print(f"❌ {测试名称} - 异常: {e}")
                测试结果[测试名称] = False
        
        # 清理测试数据
        清理测试数据()
        
        # 显示测试总结
        print("\n" + "="*60)
        print("📊 测试总结")
        print("="*60)
        
        总测试数 = len(测试结果)
        通过数 = sum(1 for 结果 in 测试结果.values() if 结果)
        失败数 = 总测试数 - 通过数
        成功率 = (通过数 / 总测试数) * 100 if 总测试数 > 0 else 0
        
        for 测试名称, 结果 in 测试结果.items():
            状态 = "✅ 通过" if 结果 else "❌ 失败"
            print(f"  {状态} {测试名称}")
        
        print(f"\n📊 统计信息:")
        print(f"  总测试数: {总测试数}")
        print(f"  通过数: {通过数}")
        print(f"  失败数: {失败数}")
        print(f"  成功率: {成功率:.1f}%")
        
        if 成功率 == 100:
            print(f"\n🎉 所有测试通过！新建删除功能修复成功。")
        else:
            print(f"\n⚠️ 有 {失败数} 个测试失败，需要进一步检查。")
        
        return 0 if 成功率 == 100 else 1
        
    except KeyboardInterrupt:
        print("\n\n👋 用户中断测试")
        清理测试数据()
        return 1
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        清理测试数据()
        return 1

if __name__ == "__main__":
    sys.exit(main())
