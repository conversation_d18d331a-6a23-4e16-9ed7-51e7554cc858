#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
浏览器多账号绿色版 - 快捷方式管理器
版本: v2.2.1
作者: 浏览器多账号绿色版团队
描述: 负责创建和管理Windows快捷方式，支持自定义图标
"""

import os
import sys
from pathlib import Path
from typing import Optional
import subprocess

class 快捷方式管理器:
    """Windows快捷方式管理器"""

    def __init__(self):
        """初始化快捷方式管理器"""
        self.项目根目录 = Path(__file__).parent.absolute()
        self.桌面路径 = self._获取桌面路径()

        # 检查win32com是否可用
        self.win32com可用 = self._检查win32com()

        print("🔗 快捷方式管理器初始化完成")
        if not self.win32com可用:
            print("⚠️ win32com不可用，将使用批处理文件作为替代方案")

    def _检查win32com(self) -> bool:
        """检查win32com是否可用"""
        try:
            import win32com.client
            return True
        except ImportError:
            return False

    def _获取桌面路径(self) -> Path:
        """获取桌面路径"""
        try:
            # 尝试从环境变量获取桌面路径
            桌面路径 = os.environ.get('USERPROFILE')
            if 桌面路径:
                桌面路径 = Path(桌面路径) / "Desktop"
                if 桌面路径.exists():
                    return 桌面路径

            # 备用方案：使用当前用户目录下的Desktop
            桌面路径 = Path.home() / "Desktop"
            if 桌面路径.exists():
                return 桌面路径

            # 如果都不存在，返回项目目录
            print("⚠️ 无法找到桌面目录，将在项目目录创建快捷方式")
            return self.项目根目录

        except Exception as e:
            print(f"⚠️ 获取桌面路径失败: {e}")
            return self.项目根目录

    def 创建快捷方式(self,
                   目标程序: Path,
                   快捷方式名称: str,
                   工作目录: Path = None,
                   参数: str = "",
                   图标路径: Path = None,
                   保存位置: Path = None) -> bool:
        """创建快捷方式

        Args:
            目标程序: 要启动的程序路径
            快捷方式名称: 快捷方式的名称
            工作目录: 程序的工作目录
            参数: 启动参数
            图标路径: 自定义图标路径
            保存位置: 快捷方式保存位置，默认为桌面

        Returns:
            bool: 创建是否成功
        """
        try:
            if not 目标程序.exists():
                print(f"❌ 目标程序不存在: {目标程序}")
                return False

            保存位置 = 保存位置 or self.桌面路径
            工作目录 = 工作目录 or 目标程序.parent

            if self.win32com可用:
                return self._创建lnk快捷方式(目标程序, 快捷方式名称, 工作目录, 参数, 图标路径, 保存位置)
            else:
                return self._创建bat快捷方式(目标程序, 快捷方式名称, 工作目录, 参数, 保存位置)

        except Exception as e:
            print(f"❌ 创建快捷方式失败: {e}")
            return False

    def _创建lnk快捷方式(self,
                      目标程序: Path,
                      快捷方式名称: str,
                      工作目录: Path,
                      参数: str,
                      图标路径: Path,
                      保存位置: Path) -> bool:
        """使用win32com创建.lnk快捷方式"""
        try:
            import win32com.client

            shell = win32com.client.Dispatch("WScript.Shell")
            快捷方式路径 = 保存位置 / f"{快捷方式名称}.lnk"

            shortcut = shell.CreateShortCut(str(快捷方式路径))
            shortcut.Targetpath = str(目标程序)
            shortcut.WorkingDirectory = str(工作目录)

            if 参数:
                shortcut.Arguments = 参数

            if 图标路径 and 图标路径.exists():
                # 如果是ICO文件，直接使用
                if 图标路径.suffix.lower() == '.ico':
                    shortcut.IconLocation = str(图标路径)
                else:
                    # 如果是其他格式，尝试转换为ICO
                    ico路径 = self._转换为ico格式(图标路径)
                    if ico路径:
                        shortcut.IconLocation = str(ico路径)

            shortcut.save()

            print(f"✅ 创建LNK快捷方式: {快捷方式路径}")
            return True

        except Exception as e:
            print(f"❌ 创建LNK快捷方式失败: {e}")
            return False

    def _创建bat快捷方式(self,
                      目标程序: Path,
                      快捷方式名称: str,
                      工作目录: Path,
                      参数: str,
                      保存位置: Path) -> bool:
        """创建.bat批处理快捷方式"""
        try:
            快捷方式路径 = 保存位置 / f"{快捷方式名称}.bat"

            # 构建批处理内容
            批处理内容 = f'@echo off\n'
            批处理内容 += f'cd /d "{工作目录}"\n'

            if 参数:
                批处理内容 += f'"{目标程序}" {参数}\n'
            else:
                批处理内容 += f'"{目标程序}"\n'

            # 写入批处理文件
            with open(快捷方式路径, 'w', encoding='utf-8') as f:
                f.write(批处理内容)

            print(f"✅ 创建BAT快捷方式: {快捷方式路径}")
            return True

        except Exception as e:
            print(f"❌ 创建BAT快捷方式失败: {e}")
            return False

    def _转换为ico格式(self, 图片路径: Path) -> Optional[Path]:
        """将图片转换为ICO格式"""
        try:
            from PIL import Image

            # 生成ICO文件路径
            ico路径 = 图片路径.parent / f"{图片路径.stem}.ico"

            # 如果ICO文件已存在，直接返回
            if ico路径.exists():
                return ico路径

            # 打开并转换图片
            with Image.open(图片路径) as img:
                # 转换为RGBA模式
                if img.mode != 'RGBA':
                    img = img.convert('RGBA')

                # 调整大小为32x32（标准图标大小）
                img = img.resize((32, 32), Image.Resampling.LANCZOS)

                # 保存为ICO格式
                img.save(ico路径, format='ICO')

            print(f"✅ 图标转换成功: {ico路径}")
            return ico路径

        except ImportError:
            print("⚠️ PIL库不可用，无法转换图标格式")
            return None
        except Exception as e:
            print(f"❌ 图标转换失败: {e}")
            return None

    def 创建浏览器快捷方式(self,
                       浏览器名称: str,
                       实例目录: Path,
                       图标类型: str = "chrome") -> bool:
        """为浏览器实例创建快捷方式"""
        try:
            # 构建路径
            chrome_exe = 实例目录 / "Chrome-bin" / "chrome.exe"
            数据目录 = 实例目录 / f"Data_{浏览器名称}"

            # 查找图标文件
            图标路径 = None
            for 扩展名 in ['.ico', '.png', '.jpg', '.jpeg']:
                图标文件 = 实例目录 / f"{图标类型}{扩展名}"
                if 图标文件.exists():
                    图标路径 = 图标文件
                    break

            # 构建启动参数
            启动参数 = f'--user-data-dir="{数据目录}" --no-first-run --no-default-browser-check'

            # 创建快捷方式
            成功 = self.创建快捷方式(
                目标程序=chrome_exe,
                快捷方式名称=浏览器名称,
                工作目录=实例目录,
                参数=启动参数,
                图标路径=图标路径
            )

            if 成功:
                print(f"✅ 浏览器快捷方式创建成功: {浏览器名称}")
            else:
                print(f"❌ 浏览器快捷方式创建失败: {浏览器名称}")

            return 成功

        except Exception as e:
            print(f"❌ 创建浏览器快捷方式异常: {e}")
            return False

    def 删除快捷方式(self, 快捷方式名称: str, 位置: Path = None) -> bool:
        """删除快捷方式"""
        try:
            位置 = 位置 or self.桌面路径

            # 尝试删除不同格式的快捷方式
            删除成功 = False
            for 扩展名 in ['.lnk', '.bat']:
                快捷方式路径 = 位置 / f"{快捷方式名称}{扩展名}"
                if 快捷方式路径.exists():
                    快捷方式路径.unlink()
                    print(f"✅ 删除快捷方式: {快捷方式路径}")
                    删除成功 = True

            if not 删除成功:
                print(f"⚠️ 未找到快捷方式: {快捷方式名称}")

            return 删除成功

        except Exception as e:
            print(f"❌ 删除快捷方式失败: {e}")
            return False

    def 获取桌面快捷方式列表(self) -> list:
        """获取桌面上的快捷方式列表"""
        try:
            快捷方式列表 = []

            if not self.桌面路径.exists():
                return 快捷方式列表

            # 查找.lnk和.bat文件
            for 扩展名 in ['.lnk', '.bat']:
                for 文件 in self.桌面路径.glob(f"*{扩展名}"):
                    快捷方式列表.append({
                        '名称': 文件.stem,
                        '路径': str(文件),
                        '类型': 扩展名[1:].upper()
                    })

            return 快捷方式列表

        except Exception as e:
            print(f"❌ 获取快捷方式列表失败: {e}")
            return []

def 测试快捷方式管理器():
    """测试快捷方式管理器功能"""
    print("🧪 开始测试快捷方式管理器...")

    try:
        # 创建管理器实例
        管理器 = 快捷方式管理器()

        # 测试基本信息
        print(f"\n📁 桌面路径: {管理器.桌面路径}")
        print(f"🔧 Win32COM可用: {管理器.win32com可用}")

        # 测试获取快捷方式列表
        print("\n📋 当前桌面快捷方式:")
        快捷方式列表 = 管理器.获取桌面快捷方式列表()
        if 快捷方式列表:
            for 快捷方式 in 快捷方式列表[:5]:  # 只显示前5个
                print(f"  {快捷方式['类型']} - {快捷方式['名称']}")
            if len(快捷方式列表) > 5:
                print(f"  ... 还有 {len(快捷方式列表) - 5} 个快捷方式")
        else:
            print("  无快捷方式或无法访问桌面")

        # 测试创建简单快捷方式（使用记事本作为测试）
        记事本路径 = Path("C:/Windows/System32/notepad.exe")
        if 记事本路径.exists():
            print(f"\n🧪 测试创建快捷方式...")
            测试成功 = 管理器.创建快捷方式(
                目标程序=记事本路径,
                快捷方式名称="测试快捷方式",
                保存位置=管理器.项目根目录  # 保存到项目目录而不是桌面
            )

            if 测试成功:
                print("✅ 快捷方式创建测试通过")
                # 清理测试文件
                测试文件 = 管理器.项目根目录 / "测试快捷方式.lnk"
                if 测试文件.exists():
                    测试文件.unlink()
                测试文件 = 管理器.项目根目录 / "测试快捷方式.bat"
                if 测试文件.exists():
                    测试文件.unlink()
                print("✅ 测试文件清理完成")
            else:
                print("❌ 快捷方式创建测试失败")

        print("\n🎯 快捷方式管理器测试完成")
        return True

    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        return False

if __name__ == "__main__":
    测试快捷方式管理器()
