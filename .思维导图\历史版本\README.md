# 思维导图历史版本管理

## 版本管理规则

### 文件命名规范
- 主导图：`主导图_YYYYMMDD.mmd`
- 专项导图：`类型_具体名称_YYYYMMDD.mmd`
- 版本备份：`原文件名_YYYYMMDD_v版本号.mmd`

### 版本控制规则
- 首次创建使用当前日期
- 重大修改（变更>30%）创建新版本备份
- 小修改直接更新原文件
- 超过30天的版本自动归档到历史版本目录

### 版本历史记录

#### v2.2.1 (2025-07-25)
- 创建标准化思维导图系统
- 建立项目角色定义体系
- 完善技术架构文档
- 整合功能模块管理

#### v2.2.0 (2025-07-24)
- 多语言支持功能
- 自动更新系统
- 语言选择界面
- 更新管理界面

#### v2.1.0 (2025-07-24)
- 快捷方式图标自定义
- 深色主题支持
- 主题选择界面
- 本地图标文件选择

#### v2.0.0 (2025-07-23)
- 项目基础架构建立
- 核心功能实现
- 插件同步系统
- 图形界面完成

## 备份策略

### 自动备份
- 每次重大更新自动创建备份
- 保留最近5个版本的完整备份
- 超过30天的版本移至历史目录

### 手动备份
- 重要里程碑版本手动备份
- 实验性功能开发前备份
- 重构代码前备份

## 恢复机制

### 版本回滚
1. 从历史版本目录选择目标版本
2. 复制到主目录并重命名
3. 更新版本号和日期
4. 验证功能完整性

### 紧急恢复
1. 使用最近的稳定版本
2. 快速功能验证
3. 记录问题和解决方案
4. 更新恢复文档
