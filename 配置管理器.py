#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
浏览器多账号绿色版 - 配置管理器
版本: v2.2.1
作者: 浏览器多账号绿色版团队
描述: 统一的配置管理系统，负责读取、写入和管理所有配置文件
"""

import json
import os
import sys
from pathlib import Path
from typing import Dict, Any, Optional
import logging

class 配置管理器:
    """统一的配置管理系统"""
    
    def __init__(self):
        """初始化配置管理器"""
        self.项目根目录 = Path(__file__).parent.absolute()
        self.项目配置文件 = self.项目根目录 / "项目配置.json"
        self.思维导图配置文件 = self.项目根目录 / "思维导图.json"
        
        # 配置缓存
        self._项目配置 = None
        self._思维导图配置 = None
        
        # 设置日志
        self._设置日志系统()
        
        # 初始化配置
        self._初始化配置()
    
    def _设置日志系统(self):
        """设置日志系统"""
        try:
            logging.basicConfig(
                level=logging.INFO,
                format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
                handlers=[
                    logging.StreamHandler(sys.stdout)
                ]
            )
            self.logger = logging.getLogger('配置管理器')
        except Exception as e:
            print(f"日志系统设置失败: {e}")
            self.logger = None
    
    def _记录日志(self, 级别: str, 消息: str):
        """记录日志"""
        if self.logger:
            if 级别 == 'info':
                self.logger.info(消息)
            elif 级别 == 'warning':
                self.logger.warning(消息)
            elif 级别 == 'error':
                self.logger.error(消息)
            elif 级别 == 'debug':
                self.logger.debug(消息)
        else:
            print(f"[{级别.upper()}] {消息}")
    
    def _初始化配置(self):
        """初始化配置文件"""
        try:
            # 检查配置文件是否存在
            if not self.项目配置文件.exists():
                self._记录日志('warning', f"项目配置文件不存在: {self.项目配置文件}")
                return False
            
            if not self.思维导图配置文件.exists():
                self._记录日志('warning', f"思维导图配置文件不存在: {self.思维导图配置文件}")
                return False
            
            # 加载配置
            self._加载项目配置()
            self._加载思维导图配置()
            
            self._记录日志('info', "配置管理器初始化成功")
            return True
            
        except Exception as e:
            self._记录日志('error', f"配置初始化失败: {e}")
            return False
    
    def _加载项目配置(self) -> bool:
        """加载项目配置"""
        try:
            with open(self.项目配置文件, 'r', encoding='utf-8') as f:
                self._项目配置 = json.load(f)
            self._记录日志('info', "项目配置加载成功")
            return True
        except Exception as e:
            self._记录日志('error', f"项目配置加载失败: {e}")
            return False
    
    def _加载思维导图配置(self) -> bool:
        """加载思维导图配置"""
        try:
            with open(self.思维导图配置文件, 'r', encoding='utf-8') as f:
                self._思维导图配置 = json.load(f)
            self._记录日志('info', "思维导图配置加载成功")
            return True
        except Exception as e:
            self._记录日志('error', f"思维导图配置加载失败: {e}")
            return False
    
    def 获取项目配置(self, 键路径: str = None) -> Any:
        """获取项目配置
        
        Args:
            键路径: 配置键路径，如 'system.language' 或 None 获取全部配置
            
        Returns:
            配置值或None
        """
        if self._项目配置 is None:
            self._加载项目配置()
        
        if self._项目配置 is None:
            return None
        
        if 键路径 is None:
            return self._项目配置
        
        # 解析键路径
        try:
            keys = 键路径.split('.')
            value = self._项目配置
            for key in keys:
                value = value[key]
            return value
        except (KeyError, TypeError):
            self._记录日志('warning', f"配置键不存在: {键路径}")
            return None
    
    def 获取思维导图配置(self, 键路径: str = None) -> Any:
        """获取思维导图配置"""
        if self._思维导图配置 is None:
            self._加载思维导图配置()
        
        if self._思维导图配置 is None:
            return None
        
        if 键路径 is None:
            return self._思维导图配置
        
        try:
            keys = 键路径.split('.')
            value = self._思维导图配置
            for key in keys:
                value = value[key]
            return value
        except (KeyError, TypeError):
            self._记录日志('warning', f"思维导图配置键不存在: {键路径}")
            return None
    
    def 更新项目配置(self, 键路径: str, 值: Any) -> bool:
        """更新项目配置"""
        try:
            if self._项目配置 is None:
                self._加载项目配置()
            
            if self._项目配置 is None:
                return False
            
            # 解析键路径并设置值
            keys = 键路径.split('.')
            config = self._项目配置
            for key in keys[:-1]:
                if key not in config:
                    config[key] = {}
                config = config[key]
            config[keys[-1]] = 值
            
            # 保存到文件
            return self._保存项目配置()
            
        except Exception as e:
            self._记录日志('error', f"更新项目配置失败: {e}")
            return False
    
    def _保存项目配置(self) -> bool:
        """保存项目配置到文件"""
        try:
            with open(self.项目配置文件, 'w', encoding='utf-8') as f:
                json.dump(self._项目配置, f, ensure_ascii=False, indent=2)
            self._记录日志('info', "项目配置保存成功")
            return True
        except Exception as e:
            self._记录日志('error', f"项目配置保存失败: {e}")
            return False
    
    def 获取Chrome路径(self) -> Optional[Path]:
        """获取Chrome程序路径"""
        chrome_path = self.获取项目配置('路径配置.Chrome程序路径')
        if chrome_path:
            full_path = self.项目根目录 / chrome_path.lstrip('./')
            return full_path if full_path.exists() else None
        return None
    
    def 获取浏览器实例目录(self) -> Path:
        """获取浏览器实例目录"""
        instance_dir = self.获取项目配置('路径配置.浏览器实例目录')
        if instance_dir:
            full_path = self.项目根目录 / instance_dir.lstrip('./')
            full_path.mkdir(exist_ok=True)
            return full_path
        return self.项目根目录 / "浏览器实例"
    
    def 获取默认图标目录(self) -> Path:
        """获取默认图标目录"""
        icon_dir = self.获取项目配置('路径配置.图标资源目录')
        if icon_dir:
            return self.项目根目录 / icon_dir.lstrip('./')
        return self.项目根目录 / "默认图标"
    
    def 检查配置完整性(self) -> Dict[str, bool]:
        """检查配置文件完整性"""
        结果 = {
            '项目配置文件存在': self.项目配置文件.exists(),
            '思维导图配置文件存在': self.思维导图配置文件.exists(),
            '项目配置加载成功': self._项目配置 is not None,
            '思维导图配置加载成功': self._思维导图配置 is not None,
            'Chrome程序存在': self.获取Chrome路径() is not None,
            '浏览器实例目录存在': self.获取浏览器实例目录().exists(),
            '默认图标目录存在': self.获取默认图标目录().exists()
        }
        
        return 结果

def 测试配置管理器():
    """测试配置管理器功能"""
    print("🧪 开始测试配置管理器...")
    
    try:
        # 创建配置管理器实例
        配置器 = 配置管理器()
        
        # 测试配置完整性检查
        print("\n📋 配置完整性检查:")
        完整性结果 = 配置器.检查配置完整性()
        for 项目, 状态 in 完整性结果.items():
            状态图标 = "✅" if 状态 else "❌"
            print(f"  {状态图标} {项目}: {状态}")
        
        # 测试获取配置
        print("\n📖 配置读取测试:")
        项目名称 = 配置器.获取项目配置('项目信息.名称')
        print(f"  项目名称: {项目名称}")
        
        版本号 = 配置器.获取项目配置('项目信息.版本')
        print(f"  版本号: {版本号}")
        
        默认语言 = 配置器.获取项目配置('系统配置.默认语言')
        print(f"  默认语言: {默认语言}")
        
        # 测试路径获取
        print("\n📁 路径配置测试:")
        chrome_path = 配置器.获取Chrome路径()
        print(f"  Chrome路径: {chrome_path}")
        
        实例目录 = 配置器.获取浏览器实例目录()
        print(f"  浏览器实例目录: {实例目录}")
        
        图标目录 = 配置器.获取默认图标目录()
        print(f"  默认图标目录: {图标目录}")
        
        # 检查所有必要配置是否存在
        必要配置 = [
            ('项目信息.名称', 项目名称),
            ('项目信息.版本', 版本号),
            ('系统配置.默认语言', 默认语言)
        ]
        
        配置测试通过 = all(值 is not None for _, 值 in 必要配置)
        路径测试通过 = chrome_path is not None and 实例目录.exists()
        
        print(f"\n🎯 测试结果:")
        print(f"  配置读取: {'✅ 通过' if 配置测试通过 else '❌ 失败'}")
        print(f"  路径检查: {'✅ 通过' if 路径测试通过 else '❌ 失败'}")
        
        总体结果 = 配置测试通过 and 路径测试通过
        print(f"  总体结果: {'✅ 测试通过' if 总体结果 else '❌ 测试失败'}")
        
        return 总体结果
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        return False

if __name__ == "__main__":
    测试配置管理器()
