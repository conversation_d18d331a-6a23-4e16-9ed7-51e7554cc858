import{t as e,v as t,w as n,x as o,y as i,z as s,A as a,B as r,C as l,D as c,E as d,F as u,X as h,G as m,H as f,I as b,f as g,e as p,J as v,K as k,L as x,M as I,N as y,O as w,P as M,Q as T,R as _,S as L,i as E}from"./util-2d803e2a.js";import{o as C,a as U,t as S,c as D,b as B,i as P,d as A,e as R,f as V,r as q}from"./tool-aa30449a.js";import{s as W,e as G}from"./stat-b988e0dc.js";const F={type:"black",domains:["v.youku.com","iqiyi.com","v.qq.com","ixigua.com","weibo.com","cctv.com","huya.com","douyu.com","bilibili.com","bilibili.to","163.com","youdao.com","zhihu.com","haokan.baidu.com","video.sina.com.cn","zjstv.com","qq.com","64memo.com","64tianwang.com","asp.fgmtv.org","bannedbook.net","bannedbook.org","beijingzx.org","china21.org","dongtaiwang.com","epochtimes.com","falundafa.org","falundafamuseum.org","fgmtv.org","hrichina.org","internetfreedom.org","maxtv.cn","mhradio.org","minghui.org","mingjingnews.com","ntdtv.com","rfa.org","secretchina.com","tuidang.org","publicdbhost.dmca.gripe","ai-course.cn","mingpao.com","xiaohongshu.com","douyin.com","le.com","pptv.com","mgtv.com","migu.cn","fun.tv","sohu.com","kuaishou.com"],black_list:["*.aliyundrive.com"]},H=()=>{let h,m={...F},f=!1,b=!1;const g=chrome.i18n.getMessage("context_title");let p=new Set(m.black_list),v=m.black_list,k=m.domains;const x=e=>{b=e&&!0===e.isInstallThunderKey},I=()=>{h=null},y=e=>{h=chrome.runtime.connectNative("com.thunder.chrome.host"),h.onMessage.addListener(x),h.onDisconnect.addListener(I);const t=navigator.userAgent;h.postMessage({ua:t,...e})};chrome.runtime.onMessage.addListener((i,s,a)=>{if(i.name===C.xl_call_function)switch(i.method){case U.startThunder:y(i);break;case U.getWebsiteDomains:a({websiteDomains:k});break;case U.addBlackListWebsite:M(i),e(S.PAGE_DISABLE,i.args[1]),t(l.websiteBlacklistArr,v);break;case U.removeBlackListWebsite:T(i),e(S.ENABLE,i.args[1]),t(l.websiteBlacklistArr,v);break;case U.trackEvent:W.apply(null,i.args)}else{if(i.name===C.CheckEnabled)return chrome.runtime.sendNativeMessage("com.thunder.chrome.host",{commandkey:"1"},e=>{b=e&&!0===e.isInstallThunderKey,a({websiteBlacklist:v,isInstallThunder:b})}),!0;if(i.name===C.xl_download){const{linkUrl:e,refererUrl:t,from:a}=i;n({url:s.tab.url}).then(n=>{y({linkurl:e,pageurl:t,cookie:n})}),fetch(e,{method:"HEAD"}).then(n=>{const i=(n.headers.get("Content-Length")/1024/1024).toFixed(2);W(1022,923,"value1="+encodeURIComponent(t||"")+"&value5="+i+"&value6="+encodeURIComponent(e||"")+"&value7="+a+"&value8="+o(e||""))})}else if(i.name===C.xl_video_show){const{videoSrc:e}=i;fetch(e,{method:"HEAD"}).then(t=>{const n=(t.headers.get("Content-Length")/1024/1024).toFixed(2);W(1022,922,"value1="+encodeURIComponent(s.tab.url||"")+"&value5=true&value6="+n+"&value7="+encodeURIComponent(e||"")+"&value8=0&value9="+o(e))})}else if(i.name===C.xl_sniff_video_info)m.isStat&&W(1022,935,"value1="+encodeURIComponent(s.tab.url)+"&value2="+i.videoType+"&value5="+i.fileUrlSuffix+"&value6="+i.videoDuration+"&value7="+i.videoSrc);else if("xl_stat"===i.name){const{eventId:e,extParam:t={}}=i;G(1022,e,t)}}}),chrome.downloads.onChanged.addListener((function(e){e.state&&e.state.current,e.totalBytes&&e.totalBytes.current})),chrome.downloads.onDeterminingFilename.addListener(async e=>{if(p.has(e.referrer)||f||!b||!i(e.referrer)||s(e.finalUrl))return;if(""!==e.referrer&&"about:blank"!==e.referrer||a({active:!0,currentWindow:!0},e=>{e&&(""!==e.url&&"about:blank"!==e.url||chrome.tabs.remove(e.id))}),e.fileSize<2097152)return;(e=>{const{id:t}=e;chrome.downloads.cancel(t,(function(){chrome.downloads.erase({id:t},(function(){}))}))})(e),W(1022,918,"value5="+encodeURIComponent(e.finalUrl));const t=await n({url:e.referrer});y({linkurl:e.finalUrl,pageurl:e.referrer,filename:e.filename,cookie:t})}),chrome.contextMenus.onClicked.addListener(async e=>{const t=e.linkUrl?e.linkUrl:e.srcUrl,o=await n({url:e.pageUrl});y({linkurl:t,pageurl:e.pageUrl,cookie:o})}),chrome.tabs.onActivated.addListener(e=>{a({active:!0,currentWindow:!0},e=>{e&&_(e)})}),chrome.tabs.onUpdated.addListener((e,t,n)=>{"complete"===t.status&&_(n)});const w=async()=>{(async()=>{v=await u("websiteBlacklistArr"),p=new Set(v)})(),m=await c(),m.black_list.forEach(e=>{p.add(e)}),v=Array.from(p),k=m.domains,t(l.websiteBlacklistArr,v)},M=e=>{f=!0;const t=e.args[0];p.add(t),v=Array.from(p)},T=e=>{f=!1;const t=e.args[0];p.delete(t),v=Array.from(p)},_=t=>{if(!i(t.url))return;const n=r.exec(t.url)[0];p.has(n)?(e(S.PAGE_DISABLE,t.id),f=!0):(e(S.ENABLE,t.id),f=!1)};(async()=>{(async()=>{try{const e=await d("com.thunder.chrome.host",{commandkey:"1"});if(b=e&&!0===e.isInstallThunderKey,!b)return void W(1022,919);W(1022,916,"value1=1")}catch(e){b=!1,W(1022,919)}})(),chrome.contextMenus.create({id:"down_menu",type:"normal",title:g,contexts:["link","image"]}),w(),W(1022,920)})()};function O(){this.listeners=[],this.valid=!1}O.prototype={isThunderXSupportWatch:function(e,t){const n=this;h.postMessage("GetThunderInfo",[],void 0,(function(o,i){let s="";o&&(s=i[0].thunderVersion);let a=!1;s.length>0&&m(s,"10.1.24.578")>=0&&(a=!0),n.valid=a,"function"==typeof t&&t.apply(e,[a])}))},setBHOConfig:function(e,t,n){if(!this.valid)return;for(let o=0;o<this.listeners.length;++o)this.listeners[o].section===e&&this.listeners[o].key===t&&(this.listeners[o].currentValue=n);f({url:"http://127.0.0.1:5021/setbhoconfig?section="+e+"&key="+t+"&value="+n,type:"GET",success:function(e){},error:function(e){}})},setConfig:function(e,t,n){if(!this.valid)return;f({url:"http://127.0.0.1:5021/setconfig",type:"POST",data:{section:e,key:t,value:n},success:function(e){},error:function(e){}})},query:function(e,t,n){const o="http://127.0.0.1:5021/getbhoconfig?section="+e.section+"&key="+e.key;f({url:o,type:"GET",success:function(n){if(n&&0===n.code&&n.value&&n.value!==e.currentValue){const t=e.currentValue;e.currentValue=n.value,e.cb.apply(e.l,[n.value,t])}"function"==typeof t&&t(n)},error:function(e){"function"==typeof n&&n(e)}})},onQueryTimer:function(){this.queryResultCount=0,this.queryCount=this.listeners.length;const e=this;for(let t=0;t<this.listeners.length;++t){const n=this.listeners[t];this.query(n,(function(){e.queryResultCount++,e.queryCount===e.queryResultCount&&e.setQueryTimer()}),(function(){e.queryResultCount++,e.queryCount===e.queryResultCount&&e.setQueryTimer()}))}},setQueryTimer:function(){do{if(this.listeners.length<=0)break;if(this.timer)break;var e=this;this.timer=setTimeout((function(){e.onQueryTimer(),e.timer=null}),5e3)}while(0)},addWatch:function(e,t,n,o,i){if(this.valid){for(let i=0;i<this.listeners.length;++i)if(this.listeners[i].l===e&&this.listeners[i].cb===t&&this.listeners[i].section===n&&this.listeners[i].key===o)return;this.listeners.push({l:e,cb:t,section:n,key:o,currentValue:i}),this.setQueryTimer()}},updateWatchValue:function(e,t,n,o,i){for(let s=0;s<this.listeners.length;++s)if(this.listeners[s].l===e&&this.listeners[s].cb===t&&this.listeners[s].section===n&&this.listeners[s].key===o){this.listeners[s].currentValue=i;break}},removeWatchValue:function(e,t,n,o){for(let i=this.listeners.length-1;i>=0;--i)this.listeners[i].l!==e||this.listeners[i].cb!==t||n&&this.listeners[i].section!==n||o&&this.listeners[i].key!==o||this.listeners.splice(i,1);this.listeners.length<=0&&this.timer&&(clearTimeout(this.timer),this.timer=null)},init:function(){this.isThunderXSupportWatch()}};const N=new O,$="https://sl-m-ssl.xunlei.com/entry/browser-plugin",j=async e=>{try{const t=await fetch("https://api-shoulei-ssl.xunlei.com/activity-v2/game_depend?activity_id=55555",{headers:{"x-client-plugin-id":e}});return await t.json()}catch(t){}},z=async(e,t)=>{const n={method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({url:e,event:"xlppc-fluentplay-website-block",title:t})};try{const e=await(o=15e3,(e,t)=>{const n=new AbortController;return(t=t||{}).signal=n.signal,setTimeout(()=>{n.abort()},o),fetch(e,t)})("https://api-shoulei-ssl.xunlei.com/xlppc.blacklist.api/v1/check",n),{data:t}=await e.json();return"accept"===t.result}catch(i){return"AbortError"===i.name||i.message.includes("abort")}var o},X=()=>{function e(){this.headers={},this.headers["user-agent"]="",this.headers.referer="",this.headers.cookie="",this.headers["content-type"]="",this.headers["content-disposition"]="",this.headers.host="",this.headers["content-length"]=0,this.headers["access-control-allow-origin"]="",this.url="",this.fileName="",this.ext="",this.postData="",this.tabId=void 0}function t(){this.pluginEnabled=!0,this.exception=!1,this.exceptionTimerId=void 0,this.blackListPageArray=[],this.blackListWebsiteArray=[],this.videoConfigGetted=!1,this.videoConfigs=null,this.alwaysIgnoreList=[],this.monitorVideo=!0,this.isShortcutEnable=!0,this.limitSizeInfo=void 0,this.monitorEmule=!1,this.monitorMagnet=!1,this.monitorTradition=!1,this.monitorIE=!1,this.enabledCapture=!0,this.monitorDomains="",this.filterDomains="",this.monitorFileExts="",this.bUseChromeDownloadAPI=!!chrome.downloads,this.isShowRecallInfo=!1,this.isHiddenRecallBadge=!0,this.recallTimer=null,this.webPeerId="",this.triggerNotificationsTabId=null,this.backgroundPageExport={trackEvent:(e,t,n)=>{this.trackEvent(e,t,n)},onFeedback:()=>{this.feedback()},setPluginEnabled:e=>{this.setPluginEnabled(e)},startThunder:()=>{h.postMessage("DownLoadByThunder",[])},removeBlackListPage:(e,t)=>{this.removeBlackListPage(e,t)},removeBlackListWebsite:(e,t,n,o)=>{this.removeBlackListWebsite(e,t,n,o)},addBlackListPage:(e,t)=>{this.addBlackListPage(e,t)},addBlackListWebsite:(e,t)=>{this.addBlackListWebsite(e,t)},isException:()=>this.exception,isPluginEnabled:()=>this.pluginEnabled,isVideoMonitor:()=>this.monitorVideo,setMonitorVideoTags:e=>{this.setMonitorVideoTags(e)},isMultiSelectShortcutEnable:()=>this.isShortcutEnable,setMultiSelectShortcutEnable:e=>{this.setMultiSelectShortcutEnable(e)},setMonitorFileExts:e=>{this.notifyThunderMonitorFileExts(e)},getMonitorFileExts:()=>this.monitorFileExts,getLimitSizeInfo:()=>this.limitSizeInfo,setLimitSizeInfo:(e,t)=>{this.setLimitSizeInfo(e,t)},isUseChromeDownloadAPI:()=>this.bUseChromeDownloadAPI,isMonitorDomain:e=>this.isMonitorDomain(e),checkIsPageInUserBlackList:e=>this.checkIsPageInUserBlackList(e),enterMultiDownload:(e,t)=>{this.enterMultiDownload(e,t)}}}t.prototype={requestItems:{},blockDownload:!1,tabUrls:{},currentTabId:void 0,m3u8VideoMap:new Map,isValidDownload:function(e){let t="";const n=e.headers["content-disposition"];if(n.length>0&&(t=b(n)),0===t.length&&(t=g(e.url)),0===t.length)return!1;e.fileName=t;const o=e.headers["content-type"];if(-1!==o.indexOf("text/")&&(-1===o.indexOf("text/multipart")||0===t.length))return!1;const i=p(t);return e.ext=i,!!this.canDownload(e)&&this.isMonitorFileExt(i)},allowPromptThunder:function(e){e&&chrome.storage.local.get(t=>{t.xl_prompt_close&&e(!1);let n=t.xl_prompt_limit_size;n&&!isNaN(n)||(n=104857600),n=Number(n),e(!0,n)})},tryThunderGuide:function(e){do{let t=e.url;if(!t)break;this.allowPromptThunder((function(n,o){if(n){t=t.toLowerCase();let n="";if(0===t.indexOf("http://")||0===t.indexOf("https://")){const t=parseInt(e.headers["content-length"]);t&&t>o&&(n="当前文件过大，建议安装迅雷，启用高速下载")}if(n){const t=e.tabId;chrome.tabs.sendMessage(t,{name:"ThunderSupportReminder",text:n})}}}))}while(0)},updateContextMenu:function(e){chrome.contextMenus.update("ThunderContextMenu",{enabled:e},(function(){chrome.runtime.lastError})),chrome.contextMenus.update("ThunderContextMenu_MultiDownload",{enabled:e},(function(){chrome.runtime.lastError}))},updateToolbarBadgeText:function(e,t){const n={text:e,tabId:t};chrome.action.setBadgeBackgroundColor({color:[0,0,0,0]}),chrome.action.setBadgeText(n)},showRecallBadge:function(e,t){const n={text:e,tabId:t};chrome.action.setBadgeBackgroundColor({color:"#dd0c02"}),chrome.action.setBadgeText(n)},updateToolbarTips:function(e,t){const n={title:e,tabId:t};chrome.action.setTitle(n)},updateBrowserActionIcon:function(e,t){const n={path:e,tabId:t};chrome.action.setIcon(n)},setToolBarStatus:function(e,t){this.updateBrowserActionIcon(e.icon,t),this.updateToolbarTips(e.tips,t),this.updateToolbarBadgeText(e.badgeText,t),!this.isHiddenRecallBadge&&this.isShowRecallInfo&&this.showRecallBadge("1",t)},invokeThunder:function(e){let t=e.headers.referer||"";t=t.concat("#@$@#"),t=t.concat(1,"#@$@#"),t=t.concat(e.url,"#@$@#"),t=t.concat(e.fileName,"#@$@#"),t=t.concat(navigator.userAgent,"#@$@#"),e.headers.cookie.length>0&&(t=t.concat(e.headers.cookie,"#@$@#")),t=t.concat("","#@$@#"),t=t.concat("","#@$@#"),t=t.concat("",e.headers.stat||"","#@$@#"),W(1022,918,"value5="+encodeURIComponent(e.url||"")),h.postMessage("DownLoadByThunder",[t])},handleM3U8HttpRequest(e,t,n){const o=e.responseHeaders.filter(e=>"content-type"===e.name.toLowerCase())[0];if(void 0===o)return;const i=o.value.toLowerCase();!e.url.includes(".m3u8")||e.url.includes("stat.download.xunlei.com")||"application/vnd.apple.mpegurl"!==i&&"application/x-mpegurl"!==i||n.m3u8VideoMap.set(t.tab.url,e.url)},downloadByThunder:function(e,t,n){if(t.headers.referer&&0!==t.headers.referer.length)this.invokeThunder(t);else if(void 0===e||e<0)this.invokeThunder(t);else{const o=this;this.getHrefById(e,e=>{t.headers.referer=e,o.invokeThunder(t)},n)}},enumTabSetEnabled:function(e){const t=this;chrome.tabs.query({active:!0},(function(n){if(n)for(let o=0;o<n.length;o++){const i=n[o],s=t.isMonitorDomain(i.url),a=!t.checkIsPageInUserBlackList(i.url);chrome.tabs.sendMessage(i.id,{name:"UpdateWebsiteEnabled",enable:s}),chrome.tabs.sendMessage(i.id,{name:"UpdatePageEnabled",enable:a}),t.setToolbarStatus(t.exception,e,s,a,i.id)}})),t.exception},setPluginEnabled:function(e){this.notifyAllTabs({name:"UpdatePluginEnabled",enable:e,exception:this.exception}),this.pluginEnabled=e,this.enumTabSetEnabled(e),this.updateContextMenu(e),h.postMessage("SetPluginEnabled",[e])},notifyAllTabs:function(e){do{if(!e)break;chrome.windows.getAll({populate:!0},t=>{if(t)for(const n in t)for(const o in t[n].tabs){const i=t[n].tabs[o];i.id>=0&&chrome.tabs.sendMessage(i.id,e)}})}while(0)},queryTabs:function(e,t){chrome.tabs.query(e,(function(e){if(e)for(let n=0;n<e.length;n++)e[n].id>=0&&t(e[n])}))},setMonitorVideoTags:function(e){this.queryTabs({active:!0},(function(t){chrome.tabs.sendMessage(t.id,{name:"UpdateMoniterVideoTags",enable:e})})),this.monitorVideo=e,chrome.storage.local.set({video_monitor:e})},setMultiSelectShortcutEnable:function(e){this.queryTabs({active:!0},(function(t){chrome.tabs.sendMessage(t.id,{name:"UpdateMultiSelectShortcutEnable",enable:e})})),this.isShortcutEnable=e,chrome.storage.local.set({multi_select_shortcut_enable:e})},onAddBlackListPage:function(e,t,n){e&&t[0].retVal&&(this.queryTabs({active:!0},(function(e){chrome.tabs.sendMessage(e.id,{name:"UpdatePageEnabled",enable:!1})})),this.blackListPageArray[this.blackListPageArray.length]=n[0])},addBlackListPage:function(e,t){for(const n in this.blackListPageArray)if(this.blackListPageArray[n]===e)return;h.postMessage("AddBlackListPage",[e],this,this.onAddBlackListPage),this.setToolbarStatus(this.exception,this.pluginEnabled,!0,!1,t)},onRemoveBlackListPage:function(e,t,n){if(e&&t[0].retVal){this.queryTabs({active:!0},(function(e){chrome.tabs.sendMessage(e.id,{name:"UpdatePageEnabled",enable:!0})}));for(const e in this.blackListPageArray)this.blackListPageArray[e]===n[0]&&delete this.blackListPageArray[e]}},removeBlackListPage:function(e,t){for(const n in this.blackListPageArray)if(this.blackListPageArray[n]===e){h.postMessage("RemoveBlackListPage",[e],this,this.onRemoveBlackListPage),this.setToolbarStatus(this.exception,this.pluginEnabled,!0,!0,t);break}},notifyThunderMonitorSites:function(){h.postMessage("SetFilters",["MonitorDomain",this.monitorDomains])},notifyThunderMonitorFileExts:function(e){this.monitorFileExts=e,h.postMessage("SetFilters",["MonitorFileExt",this.monitorFileExts])},onAddOldBlackListWebsite:function(e,t,n){e&&t[0].retVal&&(this.blackListWebsiteArray[this.blackListWebsiteArray.length]=n[0])},addOldBlackListWebsite:function(e){for(const t in this.blackListWebsiteArray)if(this.blackListWebsiteArray[t]===e)return;h.postMessage("AddBlackListWebsite",[e],this,this.onAddOldBlackListWebsite)},addBlackListWebsite:function(e,t){this.isMonitorDomain(e)&&(this.addMonitorDomain(e)&&(this.notifyThunderMonitorSites(),this.addOldBlackListWebsite(e),this.queryTabs({active:!0},(function(e){chrome.tabs.sendMessage(e.id,{name:"UpdateWebsiteEnabled",enable:!1})}))),this.setToolbarStatus(this.exception,this.pluginEnabled,!1,!0,t))},onRemoveOldBlackListWebsite:function(e,t,n){if(e&&t[0].retVal)for(const o in this.blackListWebsiteArray)this.blackListWebsiteArray[o]===n[0]&&delete this.blackListWebsiteArray[o]},removeOldBlackListWebsite:function(e,t,n,o){for(const i in this.blackListWebsiteArray)if(this.blackListWebsiteArray[i]===e){h.postMessage("RemoveBlackListWebsite",[e],this,this.onRemoveOldBlackListWebsite);break}},removeBlackListWebsite:function(e,t,n,o){this.isMonitorDomain(e)||this.removeMonitorDomain(e)&&(this.notifyThunderMonitorSites(),this.removeOldBlackListWebsite(e,t,n,o),void 0===o&&(o=!this.checkIsPageInUserBlackList(t)),this.queryTabs({active:!0},(function(e){chrome.tabs.sendMessage(e.id,{name:"UpdateWebsiteEnabled",enable:!0})})),this.setToolbarStatus(this.exception,this.pluginEnabled,!0,o,n))},enterMultiDownload:function(e,t){chrome.tabs.sendMessage(e,{name:"EnterMultiSelect",tabId:e})},checkIsPageInUserBlackList:function(e){let t=!1;for(const n in this.blackListPageArray)if(e===this.blackListPageArray[n]){t=!0;break}return t},checkIsPageVideoEnable:function(e){let t=!1;if(!this.videoConfigs)return!1;switch(this.videoConfigs.type){case"disable":break;case"white":t=!(!this.videoConfigs.domains||!D(e,this.videoConfigs.domains));break;case"black":t=!this.videoConfigs.domains||!D(e,this.videoConfigs.domains)}return t},canDownload:function(e){const t=e.tabId,n=e.url;if(e.ext,!this.pluginEnabled)return!1;if(!this.enabledCapture)return!1;let o="";return o=this.tabUrls[t]&&""!==this.tabUrls[t]?this.tabUrls[t]:e.headers.referer||"",!!this.isMonitorDomain(o)&&(!!this.isIgnoreDomain(o)&&(!this.checkIsPageInUserBlackList(o)&&!!this.isMoniterUrl(t,n,o)))},isValidUrlAndMonitorProtocol:function(e){if(0===e.length)return!1;const t=e,n=t.indexOf(":");if(-1===n)return!1;const o=t.substr(0,n+1).toUpperCase();if(""===o)return!1;let i=!0;return-1!=="ED2K://".indexOf(o)?!1===this.monitorEmule&&(i=!1):-1!=="MAGNET:?".indexOf(o)?!1===this.monitorMagnet&&(i=!1):-1!=="HTTP://HTTPS://FTP://THUNDER://MMS://MMST://RTSP://RTSPU://XLAPP://".indexOf(o)?!1===this.monitorTradition&&(i=!1):i=!1,i},isIgnoreDomain:function(e){if(0===e.length)return!0;const t=B(e);if(""===t)return!0;if(!this.alwaysIgnoreList||0===this.alwaysIgnoreList.length)return!0;const n=[];for(const i in this.alwaysIgnoreList){let e=this.alwaysIgnoreList[i];0===e.indexOf("*.")&&(e=e.slice(2));const t=e.trimRight("|");n.push(t)}let o=!0;for(const i in n){const e=n[i];if(e.length>0&&-1!==t.indexOf(e)){o=!1;break}}return o},isMonitorDomain:function(e){const t=this.monitorDomains;return P(e,t)},isFilterDomain:function(e){if(0===e.length)return!1;if(0===this.filterDomains.length)return!1;const t=new Array,n=this.filterDomains.split("||");for(const s in n){const e=n[s].slice(2).toLowerCase().trimRight("|");t.push(e)}let o=!1;const i=e.toLowerCase();for(const s in t)if(t[s]>0&&-1!==i.indexOf(t[s])){o=!0;break}return o},getExtensionFileName:function(e){const t=e.replace(/(\\+)/g,"#").split("#"),n=t[t.length-1].split(".");return n[n.length-1]},isMonitorFileExt:function(e){let t=!1;return 0!==e.length&&(e=e.toLowerCase(),e+=";",-1!==this.monitorFileExts.indexOf(e)&&(t=!0),t)},isMoniterUrl:function(e,t,n){return 0!==t.length&&(!1!==this.monitorIE&&(!1!==this.isValidUrlAndMonitorProtocol(t)&&(0===n.length&&(n=t),!1!==this.isMonitorDomain(n)&&!this.isFilterDomain(n))))},setLimitSizeInfo:function(e,t){this.limitSizeInfo.enable=e,t&&!isNaN(t)&&(this.limitSizeInfo.size=parseInt(t)),chrome.storage.local.set({"take-over-limit-size-info":JSON.stringify(this.limitSizeInfo)})},removeMonitorDomain:function(e){let t=!1;if(0===e.length)return t;const n=B(e);if(""===n)return t;const o=new Array,i=this.monitorDomains.split("||");for(var s in i){let e=i[s];0===e.indexOf("*.")&&(e=e.slice(2));const t=e.trimRight("|");o.push(t)}for(s=0;s<o.length;++s)if(o[s].length>0&&-1!==n.indexOf(o[s])){i.splice(s,1),this.monitorDomains=i.join("||"),t=!0;break}return t},addMonitorDomain:function(e){if(0===e.length)return!1;const t=B(e);if(""===t)return!1;const n=new Array,o=this.monitorDomains.split("||");for(var i in o){0===(a=o[i]).indexOf("*.")&&(a=a.slice(2));const e=a.trimRight("|");n.push(e)}let s=!0;for(i=0;i<n.length;++i)if(n[i].length>0&&-1!==t.indexOf(n[i])){s=!1;break}if(s){var a="*."+t;this.monitorDomains=this.monitorDomains+"||"+a}return s},onIsDownloadURL:function(t,n,o){if(t)if(n[0].retVal){const t=new e;t.url=o[0],t.headers.cookie=o[1],t.headers.referer=o[2],this.invokeThunder(t)}else window.open(o[0])},onBeforeSendHeaders:function(t){do{if(!v(t.type))break;let n=this.requestItems[t.requestId];n||(n=new e,this.requestItems[t.requestId]=n),n.tabId=t.tabId;const o=t.url;n.url&&0!==n.url.length||(n.url=o);for(let e=0;e<t.requestHeaders.length;++e){const o=t.requestHeaders[e].name.toLowerCase(),i=t.requestHeaders[e].value;switch(o){case"user-agent":n.headers["user-agent"]=i;break;case"referer":n.headers.referer=i;break;case"cookie":n.headers.cookie=i;break;case"content-type":n.headers["content-type"]=i}}}while(0);return{}},onHeadersReceived:function(t){t.url.includes("ww1.fijidown");do{const i=t.statusCode;if(i>=300&&i<400&&304!==i)break;if(0===t.statusLine.indexOf("HTTP/1.1 204 Intercepted by the Xunlei Advanced Integration"))break;const s=t.type;if(!v(s)){"image"===s&&chrome.tabs.get(t.tabId,e=>{const n=chrome.runtime.getURL("xl-images.html");e&&e.url&&0===e.url.indexOf(n)&&chrome.tabs.sendMessage(t.tabId,{name:"xlMultiPicUpdateDetail",value:{responseHeaders:t.responseHeaders,url:t.url}})});break}let a=t.url;var o=this.requestItems[t.requestId];o?delete this.requestItems[t.requestId]:(o=new e).tabId=t.tabId;for(let e=0;e<t.responseHeaders.length;++e){const n=t.responseHeaders[e].name.toLowerCase(),i=t.responseHeaders[e].value;switch(n){case"referer":o.headers.referer=i;break;case"set-cookie":0===o.headers.cookie.length?o.headers.cookie=i:o.headers.cookie=o.headers.cookie+"; "+i;break;case"access-control-allow-origin":break;case"host":o.headers.host=i;break;case"content-disposition":o.headers["content-disposition"]=i;break;case"content-length":o.headers["content-length"]=i;break;case"content-type":o.headers["content-type"]=i}}if(0===a.length&&(a=host),o.url=a,!k(s)){0===o.fileName.length&&(o.fileName=g(o.url));let e=p(o.fileName);if(0===e.length){const t=g(o.url);e=p(t)}const t=o.headers["content-type"];if(0===t.length&&!x(e))break;if(parseInt(o.headers["content-length"])<2097152||"swf"===e)break;if(I(t))break;break}if(2!==Math.round(t.statusCode/100)&&"other"===s)break;if(!this.isValidDownload(o)){this.exception&&this.tryThunderGuide(o);break}parseInt(o.headers["content-length"]),this.blockDownload=!0,o.headers.referer&&o.headers.cookie?this.downloadByThunder(t.tabId,o):chrome.tabs.get(t.tabId,async e=>{const i=e.openerTabId;if(!o.headers.referer&&e.url&&"about:blank"!==e.url&&(o.headers.referer=e.url),o.headers.cookie)this.downloadByThunder(t.tabId,o,i);else{const e=await n({url:o.url});o.headers.cookie=e,this.downloadByThunder(t.tabId,o,i)}})}while(0);return{}},getHrefById(e,t,n){chrome.tabs.get(n||e,e=>{t(e.url)})},onTabCreated:function(e){e.url?this.tabUrls[e.id]=e.url:e.openerTabId&&this.tabUrls[e.openerTabId]?this.tabUrls[e.id]=this.tabUrls[e.openerTabId]:this.tabUrls[e.id]=""},onTabActivated:function(e){chrome.tabs.sendMessage(e.tabId,{name:"OnActivated",tabId:e.tabId}),chrome.tabs.get(e.tabId,e=>{do{if(!e)break;if(!e.url){this.setToolbarStatus(this.exception,this.pluginEnabled,!0,!0,e.id);break}0!==e.url.indexOf("http://")&&0!==e.url.indexOf("https://")&&0!==e.url.indexOf("ftp://")&&this.setToolbarStatus(this.exception,this.pluginEnabled,!0,!0,e.id)}while(0)}),this.currentTabId=e.tabId},onTabRemoved:function(e,t){e in this.requestItems&&delete this.requestItems[e],e in this.tabUrls&&delete this.tabUrls[e]},onTabUpdated:function(e,t,n){t.url&&(this.tabUrls[e]=t.url)},onQueryAllTabs:function(e){if(e&&e.length>0)for(let t=0;t<e.length;++t)this.tabUrls[e[t].id]=e[t].url},cancelChromeDownload:function(e){chrome.downloads.cancel(e.id),chrome.downloads.erase({id:e.id},(function(e){})),""!==e.referrer&&"about:blank"!==e.referrer||this.queryTabs({active:!0,currentWindow:!0},(function(e){e&&(""!==e.url&&"about:blank"!==e.url||chrome.tabs.remove(e.id))}))},onDownloadCreated:function(e){if("complete"!==e.state&&"interrupted"!==e.state)if(this.blockDownload){this.blockDownload=!1,this.cancelChromeDownload(e);const t=function(){if(e.url){if(e.filename)return e.filename;const t=e.url.split("/");return t&&t.length>0&&t.pop()}return"迅雷下载支持"};y().then(n=>{n||w(e.url,t())}).catch(n=>{w(e.url,t())})}else;},registerEventListener:function(){const t=this;chrome.runtime.onInstalled.addListener(e=>{const{reason:t}=e;"uninstall"!==t&&"update"!==t||chrome.storage.local.remove("xl_prompt_close"),"uninstall"===t&&chrome.storage.local.remove("isAgreementVisible")}),chrome.notifications.onClicked.addListener(()=>{chrome.tabs.sendMessage(this.triggerNotificationsTabId,{name:"xl_recall_entry_click",source:"notifications"})}),chrome.runtime.onMessage.addListener((function(n,i,s){var a,r;if("xl_download"===n.name){const{title:a}=i.tab,{link:r,isM3U8Video:l,cookie:c,referurl:d,stat:u,isInIframe:f,videoUIVersion:b="",from:g=""}=n,p=e=>{switch(u){case"chrome_download_video":fetch(r,{method:"HEAD"}).then(t=>{const n=(t.headers.get("Content-Length")/1024/1024).toFixed(2);G(1022,923,{value1:encodeURIComponent(d||""),value5:n,value6:encodeURIComponent(r||""),value7:g,value8:o(r||""),value9:e,videoUIVersion:b})});break;case"chrome_download_other":G(1022,946,{value1:encodeURIComponent(d||""),value2:encodeURIComponent(r||""),value7:g,videoUIVersion:b})}};return h.postMessage("GetThunderInfo",[],void 0,(function(o,i,h){if(!o)return;const f=i[0].thunderVersion;if(-1===m(f,"12.0.6")&&l)return p("fail"),void s({errType:"version",text:"M3U8下载"});l&&(n.fileName=M(".m3u8",r,a));const b=new e;b.url=r,n.fileName&&(b.fileName=n.fileName),b.headers.cookie=c,b.headers.referer=d,b.headers.stat=u,t.invokeThunder(b),p("success")})),!0}if("xl_screen"===n.name){if("close"===n.type)return void W(1022,940,"value1="+i.tab.url);if("init"===n.type){let e=function(n){if(t.m3u8VideoMap.has(i.tab.url))return chrome.webRequest.onHeadersReceived.removeListener(e),s({isM3U8Video:!0,M3U8VideoUrl:t.m3u8VideoMap.get(i.tab.url)}),!0;i.tab.id===n.tabId&&t.handleM3U8HttpRequest(n,i,t,s)};return chrome.webRequest.onHeadersReceived.addListener(e,{urls:["<all_urls>"]},["extraHeaders","responseHeaders"]),!0}const e=t.m3u8VideoMap.get(i.tab.url),o=e||n.data.params.url;return W(1022,938,"value1="+i.tab.url+"&value5="+encodeURIComponent(o||"")),h.postMessage("GetThunderInfo",[],void 0,(function(o,a,r){if(!o)return;const l=a[0].thunderVersion;if(-1===m(l,"11.4.2"))return W(1022,939,"value1=fail"),void s({errType:"version"});e&&(n.data.params.url=t.m3u8VideoMap.get(i.tab.url));const c=T(n.data);-1===m(l,"12.0.0")?chrome.tabs.create({url:c}):h.postMessage("DownLoadByThunder",[c]),W(1022,939,"value1=success")})),!0}if("xl_cloudadd"===n.name){const{title:e}=i.tab,t=i.tab.url;n.data.params.originUrl=t,n.data.params.referer=t,n.isM3U8Video&&(n.data.params.name=M(".m3u8",n.data.params.url,e));const o=n.data.params;return h.postMessage("GetThunderInfo",[],void 0,(async function(e,t,i){if(!e)return;const a=t[0].thunderVersion;if(-1===m(a,"12.0.0"))return void s({result:!1,errType:"version",text:o.isVideo?"流畅播":"存云盘"});const r=T(n.data);h.postMessage("DownLoadByThunder",[r]),s({result:!0})})),!0}if("xl_footer_show"===n.name)G(1022,945,{value1:encodeURIComponent(i.tab.url),value2:n.downloadShow,value5:n.playShow,value6:n.saveShow,value7:n.resourceList});else if("xl_footer_other_click"===n.name)W(1022,950,"value1="+n.clickId);else{if("xl_copy"===n.name){W(1022,949,"value1="+encodeURIComponent(i.tab.url)+"&value2="+encodeURIComponent(n.text));let e=!1;const t=i.tab.id;return chrome.scripting.executeScript({target:{tabId:t},args:[n],func:async function(e){try{await navigator.clipboard.writeText(e.text)}catch(t){throw new Error({error:t})}}},t=>{chrome.runtime.lastError&&(e=!1),e=!0,s({status:e})}),!0}if("VideoShow"===n.name){const e=t.m3u8VideoMap.get(i.tab.url),s=e||n.videoSrc;fetch(s,{method:"HEAD"}).then(t=>{const i=(t.headers.get("Content-Length")/1024/1024).toFixed(2);W(1022,922,"value1="+encodeURIComponent(n.referurl||"")+"&value5="+n.hasDownload+"&value6="+i+"&value7="+encodeURIComponent(s||"")+`&value8=${e?1:0}&value9=`+o(s))})}else if("EnabledCapture"===n.name)this.enabledCapture=n.capture;else if("CheckActivated"===n.name)chrome.tabs.query({url:n.url},(function(e){if(e)for(let t=0;t<e.length;t++){const n=e[t];n.active&&chrome.tabs.sendMessage(n.id,{name:"OnActivated",tabId:n.id})}}));else{if("CheckEnabled"===n.name){const e=t.pluginEnabled,o=t.monitorVideo,i=t.isShortcutEnable,a=t.isMonitorDomain(n.url),r=!t.checkIsPageInUserBlackList(n.url);return s({exception:t.exception,bPlugin:e,bMonitorVideo:o,bWebsite:a,bPage:r,bShortcutEnable:i,isShowRecallInfo:t.isShowRecallInfo}),n.topFrame&&t.setToolbarStatus(t.exception,t.pluginEnabled,a,r,n.tabId),!0}if("xl_check_url"===n.name)h.postMessage("IsDownloadURL",[n.link,n.cookie,n.referurl],t,t.onIsDownloadURL);else{if("GetConfig"===n.name){const e={...t.videoConfigs,bMonitorEmule:t.monitorEmule,bMonitorMagnet:t.monitorMagnet,bMonitorTradition:t.monitorTradition,bMonitorIE:t.monitorIE,monitorDomains:t.monitorDomains,filterDomains:t.filterDomains,monitorFileExts:t.monitorFileExts,jsqConfig:t.videoConfigs.jsq,videoTagVersion:(null==(r=null==(a=t.videoConfigs)?void 0:a.video_tag)?void 0:r.ui_version)||A};return s(e),!0}if("CheckVideoInWhiteList"===n.name)return h.postMessage("GetThunderInfo",[],void 0,(function(e,o,a){let r=!1;if(e){const e=o[0].thunderVersion;r=-1===m(e,"12.0.0")}const l=t.checkIsPageVideoEnable(n.url),c=t.pluginEnabled,d=t.monitorVideo,u=t.isMonitorDomain(i.tab.url),h=t.videoConfigs.fluent_play||R,f=t.videoConfigs.download_sniff||V;s({exception:t.exception,videoInWhiteList:l,bPlugin:c,bMonitorVideo:d,bWebsite:u,isLess12Version:r,fluentPlayConfig:h,downloadSniffConfig:f})})),!0;if("xl_chrome_iframe_keydown"===n.name)chrome.tabs.sendMessage(i.tab.id,n);else if("xl_chrome_iframe_multi_hotkey"===n.name)chrome.tabs.sendMessage(i.tab.id,n);else if("xl_download_multi_start"===n.name)W(1022,924,"value1="+encodeURIComponent(n.referurl||""));else if("xl_download_multi"===n.name){const e=n.urls.length;if(W(1022,925,"value1="+encodeURIComponent(n.referurl||"")+"&value2="+e),e>0){const t="#@$@#";let o=n.referurl||"";o=o.concat(t),o=o.concat(e,t);const i=[];for(const e of n.urls)i.push(encodeURIComponent(e)),o=o.concat(e,t),o=o.concat("",t),o=o.concat(navigator.userAgent,t),o=o.concat(n.cookie,t),o=o.concat("",t),o=o.concat("",t);W(1022,918,"value5="+i.join(",")||""),h.postMessage("DownLoadByThunder",[o])}}else if("xl_prompt_click"===n.name){let e="";switch(n.action){case"install":e="download_thunder",W(1022,952,"value1="+encodeURIComponent(i.tab.url)+"&value5="+n.source);w("https://down.sandai.net/thunder11/XunLeiWebSetup_ext.exe","XunLeiWebSetup_ext.exe");break;case"close":e="close",chrome.storage.local.set({xl_prompt_close:!0});break;case"startThunder":t.onStartupThunder({linkUrl:"",pageUrl:""})}e&&"xl_reminder_install"===n.stat&&W(1022,929,"value2="+e)}else if("xl_prompt_show"===n.name)W(1022,928,"value1="+encodeURIComponent(i.tab.url));else{if("xl_prompt_enable"===n.name){var l=s;return t.allowPromptThunder((e,t)=>{l({enable:e})}),!0}if("xl_call_function"===n.name){l=s;const e=n.method;if(t.backgroundPageExport[e]){const o=t.backgroundPageExport[e].apply(null,n.args);return o&&o.then?o.then(e=>{l(e)}):l(o),!0}}else if("xl_sniff_video_info"===n.name)t.videoConfigs.isStat&&W(1022,935,"value1="+encodeURIComponent(i.tab.url)+"&value2="+n.videoType+"&value5="+n.fileUrlSuffix+"&value6="+n.videoDuration+"&value7="+n.videoSrc);else if("xl_stat"===n.name){const e={value1:encodeURIComponent(i.tab.url)},{eventId:t,extParam:o={}}=n;G(1022,t,{...e,...o})}else{if("xl_recall_entry_click"===n.name)return h.postMessage("GetThunderInfo",[],void 0,(async function(e,o,a){let r=0,l=0;if(e){r=1;const e=o[0].thunderVersion;if(-1===m(e,"12.0.0.2160"))s({errType:"version"});else{l=1;const e=T({opt:"web:open",params:{url:`${$}?plugin_id=${t.webPeerId}`}});h.postMessage("DownLoadByThunder",[e])}}else w(q,"XunLeiWebSetup_extrecall.exe");const c=await j(t.webPeerId);t.recallTimer||(t.recallTimer=setTimeout(()=>{!async function(e){const n=await j(e);t.isShowRecallInfo=2===n.activity.status&&2===n.game_depend.status}(t.webPeerId),clearTimeout(t.recallTimer),t.recallTimer=null},6e5));const d=2===c.game_depend.status?0:1;t.isShowRecallInfo=2===c.activity.status&&2===c.game_depend.status,W(1022,954,"value1="+encodeURIComponent(i.tab.url)+"&value2="+n.source+"&value5="+r+"&value6="+l+"&value7="+d)})),!0;if("xl_install_thunder"===n.name)w(q,"XunLeiWebSetup_extrecall.exe"),W(1022,957,"value1="+encodeURIComponent(i.tab.url));else if("xl_show_action_error_dialog"===n.name)W(1022,951,"value1="+encodeURIComponent(i.tab.url)+"&value5="+n.source);else if("xl_show_recall_entry"===n.name)W(1022,953,"value1="+encodeURIComponent(n.url||i.tab.url)+"&value2="+n.source);else if("xl_show_recall_dialog"===n.name)W(1022,955,"value1="+encodeURIComponent(i.tab.url)+"&value2="+n.curIndex);else{if("xl_receive_vip"===n.name)return h.postMessage("GetThunderInfo",[],void 0,(function(e,n,o){if(!e)return W(1022,956,"value1="+encodeURIComponent(i.tab.url)+"&value2=fail"),void s({exception:!0});const a=n[0].thunderVersion;if(-1===m(a,"12.0.0.2160"))return W(1022,956,"value1="+encodeURIComponent(i.tab.url)+"&value2=fail"),void s({errType:"version",exception:!0});const r=T({opt:"web:open",params:{url:`${$}?plugin_id=${t.webPeerId}`}});h.postMessage("DownLoadByThunder",[r]),s({exception:!1}),W(1022,956,"value1="+encodeURIComponent(i.tab.url)+"&value2=success")})),!0;if("xl_show_notifications"===n.name){if(!i.tab.id)return;t.triggerNotificationsTabId=i.tab.id,chrome.notifications.create({type:"basic",iconUrl:"images/extension_logo.png",title:"【限时】没套路！点击就送迅雷会员",message:"流畅播在线视频、高速下载、最高12T云盘"},e=>{chrome.runtime.lastError||W(1022,953,"value1="+encodeURIComponent(i.tab.url)+"&value2=notifications")})}else{if("xl_check_blacklist"===n.name){const{url:e,title:t}=i.tab;return z(e,t).then(e=>{s({isAccept:e})}),!0}if("xl_cloudadd_in_iframe"===n.name){const{id:e}=i.tab;chrome.scripting.executeScript({target:{tabId:e},args:[n],func:async function(e){const t={...e};t.name="xl_cloudadd",t.data.params.cookie=document.cookie,t.data.params.webTitle=document.title;const{ext:n,url:o}=t.data.params;".m3u8"===n&&(t.data.params.name=document.title?document.title+n:o.replace(/\?.*$/,"").replace(/.*\//,"")),chrome.runtime.sendMessage(t,e=>{if(!e)return;"version"===e.errType&&function(e){const t=(n="version","ncennffkjdiamlpmcbajkmaiiiddgioo-prompt"+n);var n;if(document.getElementById(t))return;const o=document.createElement("div");o.className="xly-dialog-prompt",o.id=t,o.innerHTML=`\n                <h2>${e}调用失败</h2>\n                <a action="close" href="javascript:;" class="xly-dialog-close" title="关闭"><i class="xl-icon-close"></i></a>\n                <p class="xly-dialog-prompt__text">客户端版本过低，无法启用${e}。</p>\n                <p class="xly-dialog-prompt__text">（升级提示：主菜单-检查更新）</p>\n                <div class="xly-dialog-prompt__footer">\n                  <div class="xly-dialog-prompt__button">\n                    <button action="close" class="td-button--other" style="width:65px; border-radius:4px;" >取消</button>\n                    <button action="startThunder" class="td-button">前往升级</button>\n                  </div>\n                </div>\n                `,document.body.appendChild(o),o.querySelectorAll("a, button").forEach(e=>{e.addEventListener("click",e=>{!function(e,t,n,o){if(!e.target||!t)return;const i=e.target.getAttribute("action");i&&(e.preventDefault(),chrome.runtime.sendMessage({name:"xl_prompt_click",action:i,stat:n,source:o}));document.body.removeChild(t)}(e,o)})})}(e.text);const{url:n,name:o,ext:i,isVideo:s}=t.data.params;chrome.runtime.sendMessage({name:"xl_cloudadd_stat",from:t.from,isSuccess:e.result,data:{url:n,fileName:o,suffix:i,isVideoURL:s}})})}})}else if("xl_show_toast"===n.name){const{id:e}=i.tab;chrome.scripting.executeScript({target:{tabId:e,tabId:e},args:[n],func:function(e){let t=document.querySelector(".xl-chrome-toast");t||(t=document.createElement("div"),t.className="xl-chrome-toast xl-chrome-toast--"+e.type,t.innerHTML+=`\n                  <div class="xl-chrome-toast-img"></div>\n                  <div class="xl-chrome-toast-text">${e.text}</div>\n                `,document.documentElement.appendChild(t)),window.xl_remove_toast_timer&&clearTimeout(window.xl_remove_toast_timer),window.xl_remove_toast_timer=setTimeout(()=>{t&&document.documentElement.removeChild(t)},3e3)}})}else if("xl_cloudadd_stat"===n.name){const{url:e,isVideoURL:t,suffix:o}=n.data;let s="value1="+encodeURIComponent(i.tab.url)+"&value5="+encodeURIComponent(e);if(t)return void fetch(e,{method:"HEAD"}).then(e=>{const t=(e.headers.get("Content-Length")/1024/1024).toFixed(2);s+=`&value2=${t}&value6=${n.from}&value7=${o.slice(1)}&value8=${".m3u8"===o?1:0}&value9=${n.isSuccess?"success":"fail"}`,W(1022,947,s)}).catch(e=>{});s+="&value6="+(n.isSuccess?"success":"fail"),W(1022,948,s)}else if("xl_download_stat"===n.name){const{url:e}=i.tab,{link:t,stat:s,status:a,videoUIVersion:r="",from:l=""}=n;switch(s){case"chrome_download_video":fetch(t,{method:"HEAD"}).then(n=>{const i=(n.headers.get("Content-Length")/1024/1024).toFixed(2);G(1022,923,{value1:encodeURIComponent(e||""),value5:i,value6:encodeURIComponent(t||""),value7:l,value8:o(t||""),value9:a,videoUIVersion:r})});break;case"chrome_download_other":G(1022,946,{value1:encodeURIComponent(e||""),value2:encodeURIComponent(t||""),value7:l,videoUIVersion:r})}}else if("xl_jsq_stat"===n.name){const{url:e}=i.tab,{eventId:t,type:o,jsqHomePage:s=""}=n;G(1022,t,{value1:o,value2:encodeURIComponent(s),tabUrl:encodeURIComponent(e||"")})}}}}}}}}})),this.bUseChromeDownloadAPI&&chrome.downloads.onCreated.addListener((function(e){return t.onDownloadCreated(e)})),chrome.webRequest&&(chrome.webRequest.onHeadersReceived.addListener((function(e){return t.onHeadersReceived(e)}),{urls:["<all_urls>"]},["responseHeaders"]),chrome.webRequest.onBeforeSendHeaders.addListener((function(e){return t.onBeforeSendHeaders(e)}),{urls:["<all_urls>"]},["requestHeaders"])),chrome.tabs&&(chrome.tabs.onCreated.addListener((function(e){return t.onTabCreated(e)})),chrome.tabs.onActivated.addListener((function(e){t.onTabActivated(e)})),chrome.tabs.onRemoved.addListener((function(e,n){t.onTabRemoved(e,n)})),chrome.tabs.onUpdated.addListener((function(e,n,o){t.onTabUpdated(e,n,o)})),chrome.tabs.query({},(function(e){t.onQueryAllTabs(e)})))},setToolbarStatus:async function(e,t,n,o,i){this.isHiddenRecallBadge=await u("isHiddenRecallBadge"),e?this.setToolBarStatus(S.EXCEPTION,i):t?n&&o?this.setToolBarStatus(S.ENABLE,i):this.setToolBarStatus(S.PAGE_DISABLE,i):this.setToolBarStatus(S.DISABLE,i)},onStartupThunder:async function(t,o){const i=await n({url:t.linkUrl}),s=new e;s.url=t.linkUrl,s.headers.cookie=i,s.headers.referer=t.pageUrl,this.invokeThunder(s)},createContextMenu:function(e){var t,n,o;const i=this,s={id:"ThunderContextMenu",type:"normal",title:(null==(t=chrome.i18n)?void 0:t.getMessage)&&(null==(n=chrome.i18n)?void 0:n.getMessage("context_title"))||"使用迅雷下载",contexts:["link"],enabled:e};chrome.contextMenus.create(s,(function(){}));const a={id:"ThunderContextMenu_MultiDownload",type:"normal",title:(null==(o=chrome.i18n)?void 0:o.getMessage)&&chrome.i18n.getMessage("multi_context_title")||"进入多选下载模式 (Shift+D)",contexts:["page"],enabled:e};chrome.contextMenus.create(a,(function(){})),chrome.contextMenus.onClicked.addListener((e,t)=>{if("ThunderContextMenu"===e.menuItemId)i.onStartupThunder(e,t);else if("ThunderContextMenu_MultiDownload"===e.menuItemId){const e=t.id;chrome.tabs.sendMessage(e,{name:"EnterMultiSelect",tabId:e})}})},onGetBlackListWebsites:function(e,t,n){if(e&&t[0].retVal){let e=!1;this.blackListWebsiteArray=t[1].blackList;for(const t in this.blackListWebsiteArray)this.isMonitorDomain(this.blackListWebsiteArray[t])&&this.addMonitorDomain(this.blackListWebsiteArray[t])&&(e=!0);e&&this.notifyThunderMonitorSites()}},onGetBlackListPages:function(e,t,n){e&&(t[0].retVal?this.blackListPageArray=t[1].blackList:this.blackListPageArray=[])},onGetIsMonitorProtocol:function(e,t,n){e&&t[0].retVal&&("MonitorEmule"===n[0]?this.monitorEmule=t[1].value:"MonitorMagnet"===n[0]?this.monitorMagnet=t[1].value:"MonitorTradition"===n[0]?this.monitorTradition=t[1].value:"MonitorIE"===n[0]&&(this.monitorIE=t[1].value))},onGetFiters:function(e,t,n){e&&t[0].retVal&&("MonitorDemain"===n[0]?(this.monitorDomains=t[1].value,this.onBrowserConfigMonitorDomainChange(this.monitorDomains),h.postMessage("GetBlackListWebsites",[],this,this.onGetBlackListWebsites)):"FilterDemain"===n[0]?this.filterDomains=t[1].value:"MonitorFileExt"===n[0]&&(this.monitorFileExts=t[1].value,this.onBrowserConfigMonitorExtendNamesChange(this.monitorFileExts)))},onBrowserConfigMonitorDomainChange:function(e){if(e){this.monitorDomains=e;const t=this;chrome.windows.getAll({populate:!0},(function(e){for(const n in e)for(const o in e[n].tabs)e[n].tabs[o].id>=0&&chrome.tabs.sendMessage(e[n].tabs[o].id,{name:"UpdateMonitorDomains",monitorDomains:t.monitorDomains})})),this.enumTabSetEnabled(this.pluginEnabled)}},onBrowserConfigMonitorExtendNamesChange:function(e){this.monitorFileExts=e;const t=chrome.runtime.getURL("options.html");chrome.windows.getAll({populate:!0},n=>{for(const o in n)for(const i in n[o].tabs){const s=n[o].tabs[i];s.id>=0&&s.url&&0===s.url.toLowerCase().indexOf(t.toLowerCase())&&chrome.tabs.sendMessage(s.id,{name:"UpdateMonitorFileExts",value:e})}})},onGetPluginEnabled:function(e,t,n){do{if(!e)break;this.pluginEnabled=t[0].retVal,this.notifyAllTabs({name:"UpdatePluginEnabled",enable:this.pluginEnabled,exception:this.exception}),this.updateContextMenu(this.pluginEnabled),this.pluginEnabled?this.setToolBarStatus(S.ENABLE):this.setToolBarStatus(S.DISABLE),N.init(),h.postMessage("GetBlackListPages",[],this,this.onGetBlackListPages),h.postMessage("GetFiters",["MonitorDemain"],this,this.onGetFiters),h.postMessage("GetFiters",["FilterDemain"],this,this.onGetFiters),h.postMessage("GetFiters",["MonitorFileExt"],this,this.onGetFiters),h.postMessage("GetIsMonitorProtocol",["MonitorEmule"],this,this.onGetIsMonitorProtocol),h.postMessage("GetIsMonitorProtocol",["MonitorMagnet"],this,this.onGetIsMonitorProtocol),h.postMessage("GetIsMonitorProtocol",["MonitorTradition"],this,this.onGetIsMonitorProtocol),h.postMessage("GetIsMonitorProtocol",["MonitorIE"],this,this.onGetIsMonitorProtocol),W(1022,916,"value1="+(this.pluginEnabled?"1":"0"))}while(0)},trackEvent:function(e,t,n){W(e,t,n)},feedback:function(){h.postMessage("GetThunderInfo",[],void 0,(function(e,t,n){let o="",i="";e&&(o=t[0].peerId,i=t[0].thunderVersion);const s="http://misc-xl9-ssl.xunlei.com/client/view/dist/1.0/feedback.html?version="+i+"&pid="+o;chrome.tabs.create({url:s},(function(){}))}))},getVideoConfigs:function(e){f({url:"http://static-xl.a.88cdn.com/json/xl_chrome_ext_config.json",type:"GET",success:function(t){e(0,t)},error:function(t){e(-1)}})},pollCheckNativeMessageConnected:function(){do{if(this.exceptionTimerId)break;this.exceptionTimerId=setInterval(async()=>{const e=h.connect(),t=await y();e&&t&&(clearInterval(this.exceptionTimerId),this.exceptionTimerId=void 0,this.exception=!1,h.postMessage("GetPluginEnabled",[],this,this.onGetPluginEnabled),_.call(this))},5e3)}while(0)},onDisconnect:function(e){e||(this.pluginEnabled=!1,this.exception=!0,this.enumTabSetEnabled(this.pluginEnabled),this.notifyAllTabs({name:"UpdatePluginEnabled",enable:this.pluginEnabled,exception:this.exception}),this.pollCheckNativeMessageConnected())},init:async function(){h.attachDisconnectEvent(this,this.onDisconnect);const e=h.connect(),t=await y(),n=this;this.createContextMenu(!1),e&&t?h.postMessage("GetPluginEnabled",[],this,this.onGetPluginEnabled)&&_.call(this):(this.exception=!0,this.pluginEnabled=!1,this.setToolBarStatus(S.EXCEPTION),W(1022,919)),n.webPeerId=await L("Q");const o=await j(n.webPeerId);2===o.activity.status&&2===o.game_depend.status&&(n.isShowRecallInfo=!0),this.registerEventListener(),this.getVideoConfigs((function(e,t){n.videoConfigGetted=!0,t&&t.black_list&&t.black_list instanceof Array&&(n.alwaysIgnoreList=t.black_list),n.videoConfigs=t})),W(1022,920)}};(new t).init()};E?H():X();
