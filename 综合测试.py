#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
浏览器多账号绿色版 - 综合测试脚本
版本: v2.2.1
作者: 浏览器多账号绿色版团队
描述: 全面测试所有功能模块，确保系统完整性和稳定性
"""

import sys
import os
import time
import shutil
from pathlib import Path

def 测试启动管理器():
    """测试启动管理器功能"""
    print("🧪 测试启动管理器...")
    
    try:
        # 测试环境检查功能
        import subprocess
        result = subprocess.run([
            sys.executable, "启动管理器.py", "--check"
        ], capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            print("  ✅ 启动管理器环境检查通过")
            return True
        else:
            print(f"  ❌ 启动管理器环境检查失败: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"  ❌ 启动管理器测试异常: {e}")
        return False

def 测试配置管理器():
    """测试配置管理器功能"""
    print("🧪 测试配置管理器...")
    
    try:
        from 配置管理器 import 配置管理器
        
        配置器 = 配置管理器()
        
        # 测试基本配置读取
        项目名称 = 配置器.获取项目配置('项目信息.名称')
        版本号 = 配置器.获取项目配置('项目信息.版本')
        
        if 项目名称 and 版本号:
            print(f"  ✅ 配置读取成功: {项目名称} {版本号}")
        else:
            print("  ❌ 配置读取失败")
            return False
        
        # 测试路径配置
        chrome_path = 配置器.获取Chrome路径()
        if chrome_path and chrome_path.exists():
            print(f"  ✅ Chrome路径正确: {chrome_path}")
        else:
            print("  ❌ Chrome路径配置错误")
            return False
        
        return True
        
    except Exception as e:
        print(f"  ❌ 配置管理器测试异常: {e}")
        return False

def 测试浏览器管理器():
    """测试浏览器管理器功能"""
    print("🧪 测试浏览器管理器...")
    
    try:
        from 浏览器管理器 import 浏览器管理器
        
        管理器 = 浏览器管理器()
        
        # 测试浏览器列表获取
        浏览器列表 = 管理器.获取浏览器列表()
        print(f"  ✅ 浏览器列表获取成功，共 {len(浏览器列表)} 个实例")
        
        # 测试创建浏览器实例
        测试名称 = "综合测试浏览器"
        
        # 清理可能存在的测试实例
        实例目录 = 管理器.浏览器实例目录 / 测试名称
        if 实例目录.exists():
            shutil.rmtree(实例目录)
        
        创建成功 = 管理器.创建浏览器实例(测试名称, "chrome")
        if 创建成功:
            print(f"  ✅ 浏览器实例创建成功: {测试名称}")
        else:
            print(f"  ❌ 浏览器实例创建失败: {测试名称}")
            return False
        
        # 测试发送到桌面
        发送成功 = 管理器.发送到桌面(测试名称)
        if 发送成功:
            print(f"  ✅ 发送到桌面成功: {测试名称}")
        else:
            print(f"  ⚠️ 发送到桌面失败: {测试名称}")
        
        # 清理测试实例
        if 实例目录.exists():
            shutil.rmtree(实例目录)
            print(f"  ✅ 测试实例清理完成: {测试名称}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 浏览器管理器测试异常: {e}")
        return False

def 测试快捷方式管理器():
    """测试快捷方式管理器功能"""
    print("🧪 测试快捷方式管理器...")
    
    try:
        from 快捷方式管理器 import 快捷方式管理器
        
        管理器 = 快捷方式管理器()
        
        # 测试基本功能
        桌面路径 = 管理器.桌面路径
        if 桌面路径:
            print(f"  ✅ 桌面路径获取成功: {桌面路径}")
        else:
            print("  ❌ 桌面路径获取失败")
            return False
        
        # 测试win32com可用性
        win32com可用 = 管理器.win32com可用
        print(f"  ✅ Win32COM可用性: {win32com可用}")
        
        # 测试快捷方式列表获取
        快捷方式列表 = 管理器.获取桌面快捷方式列表()
        print(f"  ✅ 桌面快捷方式数量: {len(快捷方式列表)}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 快捷方式管理器测试异常: {e}")
        return False

def 测试图形界面():
    """测试图形界面功能"""
    print("🧪 测试图形界面...")
    
    try:
        # 测试GUI模块导入
        from 浏览器管理器GUI import 浏览器管理器GUI, 新建浏览器对话框
        print("  ✅ GUI模块导入成功")
        
        # 测试tkinter可用性
        import tkinter as tk
        root = tk.Tk()
        root.withdraw()  # 隐藏窗口
        root.destroy()
        print("  ✅ Tkinter可用性检查通过")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 图形界面测试异常: {e}")
        return False

def 测试文件完整性():
    """测试文件完整性"""
    print("🧪 测试文件完整性...")
    
    必要文件 = [
        "启动管理器.py",
        "配置管理器.py",
        "浏览器管理器.py",
        "快捷方式管理器.py",
        "浏览器管理器GUI.py",
        "功能测试.py",
        "综合测试.py",
        "项目配置.json",
        "思维导图.json",
        "requirements.txt",
        "README.md"
    ]
    
    缺失文件 = []
    for 文件 in 必要文件:
        if not Path(文件).exists():
            缺失文件.append(文件)
    
    if 缺失文件:
        print(f"  ❌ 缺失文件: {', '.join(缺失文件)}")
        return False
    else:
        print(f"  ✅ 所有必要文件存在 ({len(必要文件)} 个)")
        return True

def 测试目录结构():
    """测试目录结构"""
    print("🧪 测试目录结构...")
    
    必要目录 = [
        "GoogleChromePortable",
        "默认图标",
        "浏览器实例",
        ".思维导图",
        "备份",
        "日志",
        "temp"
    ]
    
    缺失目录 = []
    for 目录 in 必要目录:
        if not Path(目录).exists():
            缺失目录.append(目录)
    
    if 缺失目录:
        print(f"  ❌ 缺失目录: {', '.join(缺失目录)}")
        return False
    else:
        print(f"  ✅ 所有必要目录存在 ({len(必要目录)} 个)")
        return True

def 测试思维导图系统():
    """测试思维导图系统"""
    print("🧪 测试思维导图系统...")
    
    try:
        思维导图目录 = Path(".思维导图")
        if not 思维导图目录.exists():
            print("  ❌ 思维导图目录不存在")
            return False
        
        # 检查主要思维导图文件
        主要文件 = [
            "主导图_20250725.mmd",
            "项目状态分析_20250725.mmd",
            "角色定义_20250725.mmd"
        ]
        
        存在文件 = []
        for 文件 in 主要文件:
            文件路径 = 思维导图目录 / 文件
            if 文件路径.exists():
                存在文件.append(文件)
        
        print(f"  ✅ 思维导图文件: {len(存在文件)}/{len(主要文件)} 个存在")
        
        # 检查历史版本目录
        历史目录 = 思维导图目录 / "历史版本"
        if 历史目录.exists():
            print("  ✅ 历史版本目录存在")
        else:
            print("  ⚠️ 历史版本目录不存在")
        
        return len(存在文件) > 0
        
    except Exception as e:
        print(f"  ❌ 思维导图系统测试异常: {e}")
        return False

def 生成测试报告(测试结果):
    """生成测试报告"""
    print("\n" + "="*60)
    print("📊 综合测试报告")
    print("="*60)
    
    总测试数 = len(测试结果)
    通过数 = sum(1 for 结果 in 测试结果.values() if 结果)
    失败数 = 总测试数 - 通过数
    成功率 = (通过数 / 总测试数) * 100 if 总测试数 > 0 else 0
    
    print(f"测试时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"项目版本: v2.2.1")
    print(f"Python版本: {sys.version.split()[0]}")
    print()
    
    print("📋 测试项目结果:")
    for 测试名称, 结果 in 测试结果.items():
        状态 = "✅ 通过" if 结果 else "❌ 失败"
        print(f"  {状态} {测试名称}")
    
    print(f"\n📊 统计信息:")
    print(f"  总测试数: {总测试数}")
    print(f"  通过数: {通过数}")
    print(f"  失败数: {失败数}")
    print(f"  成功率: {成功率:.1f}%")
    
    if 成功率 == 100:
        print(f"\n🎉 所有测试通过！系统功能完整，可以正常使用。")
        print("🚀 项目已达到生产就绪状态！")
    elif 成功率 >= 80:
        print(f"\n✅ 大部分测试通过，系统基本可用。")
        print("⚠️ 建议修复失败的测试项目以提高稳定性。")
    else:
        print(f"\n❌ 多个测试失败，系统存在严重问题。")
        print("🔧 请优先修复失败的测试项目。")
    
    return 成功率

def main():
    """主函数"""
    print("🧪 浏览器多账号绿色版 - 综合测试")
    print("版本: v2.2.1")
    print("="*60)
    
    测试项目 = [
        ("文件完整性测试", 测试文件完整性),
        ("目录结构测试", 测试目录结构),
        ("思维导图系统测试", 测试思维导图系统),
        ("启动管理器测试", 测试启动管理器),
        ("配置管理器测试", 测试配置管理器),
        ("浏览器管理器测试", 测试浏览器管理器),
        ("快捷方式管理器测试", 测试快捷方式管理器),
        ("图形界面测试", 测试图形界面)
    ]
    
    测试结果 = {}
    
    try:
        for i, (测试名称, 测试函数) in enumerate(测试项目, 1):
            print(f"\n📋 [{i}/{len(测试项目)}] {测试名称}")
            print("-" * 40)
            
            try:
                结果 = 测试函数()
                测试结果[测试名称] = 结果
                
                if 结果:
                    print(f"✅ {测试名称} - 通过")
                else:
                    print(f"❌ {测试名称} - 失败")
                    
            except Exception as e:
                print(f"❌ {测试名称} - 异常: {e}")
                测试结果[测试名称] = False
        
        # 生成测试报告
        成功率 = 生成测试报告(测试结果)
        
        # 返回适当的退出码
        return 0 if 成功率 == 100 else 1
        
    except KeyboardInterrupt:
        print("\n\n👋 用户中断测试")
        return 1
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
