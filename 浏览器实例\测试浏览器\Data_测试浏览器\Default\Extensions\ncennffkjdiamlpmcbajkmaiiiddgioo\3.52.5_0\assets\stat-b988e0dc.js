import{k as e,V as n,H as t,X as o,S as r,i}from"./util-2d803e2a.js";function s(e){for(const t in n){const o=new RegExp(n[t],"i").exec(e);if(null!=o)return o}return null}function c(e){const n=e.toLowerCase();let t=!0;return-1!=n.indexOf("?")&&-1==n.indexOf("magnet:?")||(t=!1),t}function a(e){const n=e.replace(/(\\+)/g,"#").split("#"),t=n[n.length-1].split(".");return t[t.length-1]}function u(e,n){let t=!1;do{if(!n)break;const o=n.getBoundingClientRect();if(e.clientX<o.left||e.clientY<o.top||e.clientX>o.right||e.clientY>o.bottom)break;t=!0}while(0);return t}function l(){const n=new Set,t=document.getElementsByTagName("body");if(0!==t.length){for(const o of t){const t=o.innerHTML.match(e);t&&t.forEach(e=>{n.add(e)})}return n}}function f(e,n){if(0==e.length)return!1;if(0==n.length)return!1;const t=[],o=n.split("||");for(const s in o){const e=o[s].slice(2).toLowerCase().trimRight("|");t.push(e)}let r=!1;const i=e.toLowerCase();for(const s in t)if(t[s]>0&&-1!=i.indexOf(t[s])){r=!0;break}return r}function d(){let e=!1;do{if(!document.activeElement)break;const n=document.activeElement,t=n.tagName.toUpperCase();if("INPUT"===t||"TEXTAREA"===t){e=!0;break}if(!n.contentEditable)break;if("true"===n.contentEditable.toLowerCase()){e=!0;break}}while(0);return e}function p(e,n){let t=void 0;do{if(!e)break;if("VIDEO"!==e.tagName.toUpperCase())break;if(e.src){if(0===e.src.toLowerCase().indexOf("blob:")&&!n)break;t=e.src;break}if(!e.children||0===e.children.length)break;for(let n=0;n<e.children.length;n++){const o=e.children[n];if("source"===o.tagName.toLowerCase()&&o.src){0!==o.src.toLowerCase().indexOf("blob:")&&(t=o.src);break}}}while(0);return t}let h=void 0;const b=function(e){const{aid:n,id:o,ext:r,thunderVersion:s,peerId:c,osVersion:a,parentProcess:u,currentTab:l={}}=e;let f="http://stat.download.xunlei.com:8099/?xlbtid=1&aid="+n+"&id="+o+"&peerid="+c+`&userid=&referfrom=100001&OS=${i?"Mac OS":"win"}&OSversion=`+a+"&productname=ThunderX&productversion="+s+"&value3="+h+"&value4="+u+"&valueT="+(new Date).getTime();r&&r.length>0&&(f+="&"+r),t({url:f,type:"GET",success:function(){},error:function(e){}})},m=function(e,n,t){o.postMessage("GetThunderInfo",[],void 0,(async function(o,i){if(o){const o=i[0].peerId,r=i[0].osVersion,s=i[0].thunderVersion,c=i[0].parentProcess;b({aid:e,id:n,ext:t,thunderVersion:s,peerId:o,osVersion:r,parentProcess:c})}else{const o=await r("Q");b({aid:e,id:n,ext:t,thunderVersion:"",peerId:o,osVersion:"",parentProcess:""})}}))};function g(e,n,o){h?m(e,n,o):t({url:chrome.runtime.getURL("manifest.json"),type:"GET",success:function(t){h=t.version,m(e,n,o)},error:function(e){}})}function k(e,n,t={}){g(e,n,function(e,n="&"){return 0===Object.keys(e).length?"":Object.keys(e).map(n=>`${encodeURIComponent(n)}=${encodeURIComponent(e[n])}`).join(n)}(t))}export{a as G,c as I,u as a,l as b,s as c,f as d,k as e,p as g,d as i,g as s};
